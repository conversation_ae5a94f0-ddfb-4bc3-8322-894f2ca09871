// Fill out your copyright notice in the Description page of Project Settings.


#include "ThingIcon.h"
#include "Engine/Texture2D.h"
#include "TheAwakener_FO/FunctionLibrary/ResourceFuncLib.h"
#include "TheAwakener_FO/GamePlay/Thing/ThingUIInfo.h"

void UThingIcon::NativeOnInitialized()
{
	Super::NativeOnInitialized();
	BackgroundImg = Cast<UImage>(GetWidgetFromName(TEXT("Background")));
	IconImg = Cast<UImage>(GetWidgetFromName(TEXT("Icon")));
	CountTextBlock = Cast<UTextBlock>(GetWidgetFromName(TEXT("CountText")));
	ThisButton = Cast<UButton>(GetWidgetFromName(TEXT("BackButton")));
	SelectMask = Cast<UImage>(GetWidgetFromName(TEXT("SelectSign")));
	if (ThisButton) ThisButton->OnClicked.AddDynamic(this, &UThingIcon::ClickMe);
	if (SelectMask) SelectMask->SetVisibility(ESlateVisibility::Hidden);
}

void UThingIcon::ClickMe()
{
	if (SelectMask)
		SelectMask->SetVisibility(ESlateVisibility::HitTestInvisible);
	if(this->FromBackpack)
	{
		switch (this->ThisThingType)
		{
		case EThingType::Item:FromBackpack->ShowItemObjHint(this->ItemObj); break;
		case EThingType::WeaponObj:FromBackpack->ShowWeaponObjHint(this->WeaponObj); break;
		case EThingType::Equipment:FromBackpack->ShowEquipmentHint(this->Equipment); break;
		default:break;
		}
	}
}


void UThingIcon::NativeOnListItemObjectSet(UObject* ListItemObject)
{
	const UThingIcon* LogicItem = Cast<UThingIcon>(ListItemObject);
	if (!LogicItem) return;
	this->Equipment = LogicItem->Equipment;
	this->WeaponObj = LogicItem->WeaponObj;
	this->ItemObj = LogicItem->ItemObj;
	this->ThisThingType = LogicItem->ThisThingType;
	this->FromBackpack = LogicItem->FromBackpack;
	this->IconPath = LogicItem->IconPath;
	this->BackgroundPath = LogicItem->BackgroundPath;
	this->Count = LogicItem->Count;
	this->ThisButton->OnClicked = LogicItem->ThisButton->OnClicked;

	Draw();
}


void UThingIcon::SetAsItemObj(UBackpack* From, FItemObj Item, FString SetIconPath, int SetCount)
{
	this->FromBackpack = From;
	this->ThisThingType = EThingType::Item;
	this->ItemObj = Item;
	
	this->IconPath = SetIconPath;
	this->Count = SetCount;

	Draw();
}

void UThingIcon::SetAsWeaponObj(UBackpack* From, FEquippedWeaponSet Item, FString SetIconPath)
{
	this->FromBackpack = From;
	this->ThisThingType = EThingType::WeaponObj;
	this->WeaponObj = Item;
	
	this->IconPath = SetIconPath;
	this->Count = 1;

	Draw();
}

void UThingIcon::SetAsEquipment(UBackpack* From, FEquipment Item, FString SetIconPath)
{
	this->FromBackpack = From;
	this->ThisThingType = EThingType::Equipment;
	this->Equipment = Item;
	
	this->IconPath = SetIconPath;
	this->Count = 1;

	Draw();
}

void UThingIcon::NativeOnItemSelectionChanged(bool bIsSelected)
{
	if (bIsSelected)
	{
		this->ClickMe();
	}else
	{
		if (SelectMask)
			SelectMask->SetVisibility(ESlateVisibility::Hidden);
	}
}


void UThingIcon::Draw()
{
	if (IconImg)
	{
		if (IconPath.IsEmpty())
		{
			IconImg->SetVisibility(ESlateVisibility::Hidden);
		}else
		{
			IconImg->SetVisibility(ESlateVisibility::HitTestInvisible);
			UTexture2D* IconTexture = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(IconPath));
			IconImg->SetBrushFromTexture(IconTexture);
		}
	}
	//背景先固定
	// if (BackgroundImg)
	// {
	// 	if (BackgroundPath.IsEmpty() == false)
	// 	{
	// 		BackgroundImg->SetVisibility(ESlateVisibility::Hidden);
	// 	}else
	// 	{
	// 		BackgroundImg->SetVisibility(ESlateVisibility::HitTestInvisible);
	// 		UTexture2D* IconTexture = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(BackgroundPath));
	// 		BackgroundImg->SetBrushFromTexture(IconTexture);
	// 	}
	// }
	if (CountTextBlock)
	{
		CountTextBlock->SetText(FText::FromString(FString::FromInt(Count)));
	}
}