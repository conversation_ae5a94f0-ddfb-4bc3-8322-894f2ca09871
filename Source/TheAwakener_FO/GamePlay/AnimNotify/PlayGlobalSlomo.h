// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "PlayGlobalSlomo.generated.h"

/**
 * 开始子弹时间
 */
UCLASS()
class THEAWAKENER_FO_API UPlayGlobalSlomo : public UAnimNotify
{
	GENERATED_BODY()
public:
	//减速率 0-1.f
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float SlowDownRate = 0.5f;
	//结束时恢复到多少
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float NormalRate = 1.0f;
	//持续时间
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	float Duration = 0.f;
	//优先级 低优先级的会被高优先级覆盖
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int Priority =0;
	
	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};


