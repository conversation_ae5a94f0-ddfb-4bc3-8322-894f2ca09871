// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "Animation/AnimNotifyQueue.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Item/ItemObj.h"
#include "UseItemObj.generated.h"

/**
 * 使用道具（ItemObj）的Notify
 */
UCLASS()
class THEAWAKENER_FO_API UUseItemObj : public UAnimNotify
{
	GENERATED_BODY()
public:
	//使用的手法，用的肯定是Role里面的ItemObjs[FocusItemIndex]对应的，如果没有，就会用失败了
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EItemUseMethod UseMethod;

	//如果使用失败，则跳转到某个Section，不写就是失败与否都不会跳转
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FName GoToSectionOnUseFail = FName();

	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
