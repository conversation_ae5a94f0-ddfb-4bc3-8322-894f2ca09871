// Fill out your copyright notice in the Description page of Project Settings.

#pragma once
#include "CoreMinimal.h"
#include "Dom/JsonObject.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "DataFuncLib.generated.h"


USTRUCT(BlueprintType)
struct FJsonFuncData
{
	GENERATED_BODY()
public:
	//function逻辑实现的类地址
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString ClassPath;
	//function的名字
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString FunctionName;
	//function的输入参数
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> Params;

	bool operator ==(const FJsonFuncData& A) const
	{
		if(ClassPath == A.ClassPath && FunctionName == A.FunctionName && Params.Num() == A.Params.Num())
		{
			for(int i = 0; i < Params.Num(); i++)
			{
				if(Params[i] != A.Params[i])
					return false;
			}
			return true;
		}
		return false;
	}

	bool operator !=(const FJsonFuncData& A) const
	{
		if(ClassPath == A.ClassPath && FunctionName == A.FunctionName && Params.Num() == A.Params.Num())
		{
			for(int i = 0; i < Params.Num(); i++)
			{
				if(Params[i] != A.Params[i])
					return true;
			}
			return false;
		}
		return true;
	}
};

USTRUCT(BlueprintType)
struct FJsonFuncDataGroup
{
	GENERATED_BODY()
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
		TArray<FJsonFuncData> JsonFuncDataGroup;
};

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UDataFuncLib : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:
	UFUNCTION(BlueprintCallable, Category="AW|FunctionLibrary")
	static bool CfgValueToBool(FString ConfigValue);

	UFUNCTION(BlueprintCallable, Category="AW|FunctionLibrary")
	static FJsonFuncData SplitFuncNameAndParams(FString InString)
	{
		FJsonFuncData TempFuncParam;
		//ClassPath
		FString ClassPath;
		FString ParamsStr;
		if (!(InString.Split(".", &ClassPath, &ParamsStr)))
		{
			UE_LOG(LogTemp, Log, TEXT("拆分ClassPath失败 - InString:%s"), *InString);
			return TempFuncParam;
		}
		TempFuncParam.ClassPath = ClassPath;
		//FuncName
		FString FunctionName;
		if (!(ParamsStr.Split("(", &FunctionName, &ParamsStr)))
		{
			UE_LOG(LogTemp, Log, TEXT("拆分FuncName失败 - InString:%s"), *InString);
			return TempFuncParam;
		}
		TempFuncParam.FunctionName = FunctionName;
		//Params
		if (ParamsStr.Len() > 0)
		{
			FString DiscardString;
			ParamsStr.Split(")", &ParamsStr, &DiscardString);
			ParamsStr.ParseIntoArray(TempFuncParam.Params, TEXT(","), true);
		}
		return TempFuncParam;
	}
	UFUNCTION(BlueprintCallable)
	static FString SplitParamBetweenSplitChar(FString InString,FString LeftSplit,FString RightSplit,FString& LeftS,FString& RightS );
	
	template <typename T>
	static TArray<T> JsonValueArrayToEnumArray(TArray<TSharedPtr<FJsonValue>> JsonValues)
	{
		TArray<T> Array;
		for (const TSharedPtr<FJsonValue> Value : JsonValues)
		{
			int32 Index = StaticEnum<T>()->GetIndexByNameString(Value->AsString());
			Array.Add(static_cast<T>(Index)); 
		}
		return Array;
	};
	static TArray<FString> JsonValueArrayToFStringArray(TArray<TSharedPtr<FJsonValue>> JsonValues);
	static FVector FStringToV3(FString Str);
	template <typename T>
	static T FStringToEnum(FString Str)
	{
		int32 Index = StaticEnum<T>()->GetIndexByNameString(Str);
		return static_cast<T>(Index); 
	};
	template<typename EnumType>
	static FString EnumToFString(const EnumType EnumeratorValue)
	{
		FString Left = "";
		FString Right = "";
		UEnum::GetValueAsString(EnumeratorValue).Split("::", &Left, &Right);
		return Right;
	}
	//获取基础怪物ID
	// 返回值 true:裁剪成功 false:本身就是基础ID
	UFUNCTION(BlueprintPure,BlueprintCallable)
	static bool ClipAtlerId(FString& MobId);
// ---------- 封装的json读取 ----------
	static double AwGetNumberField(TSharedPtr<FJsonObject> JsonObject, const FString& FieldName, double DefaultNumber = 0);
	static int32 AwGetNumberField(TSharedPtr<FJsonObject> JsonObject, const FString& FieldName, int32 DefaultNumber = 0);
	static uint32 AwGetNumberField(TSharedPtr<FJsonObject> JsonObject, const FString& FieldName, uint32 DefaultNumber = 0);
	static int64 AwGetNumberField(TSharedPtr<FJsonObject> JsonObject, const FString& FieldName, int64 DefaultNumber = 0);
	static FString AwGetStringField(TSharedPtr<FJsonObject> JsonObject, const FString& FieldName, FString DefaultString = "");
	static TArray<FString> AwGetStringArrayField(TSharedPtr<FJsonObject> JsonObject, const FString& FieldName,
		TArray<FString> DefaultStringArray = TArray<FString>());
	static TArray<FString> AwGetJsonKeyField(TSharedPtr<FJsonObject> JsonObject);
	template<typename TEnum>
	static TArray<TEnum> AwGetEnumArrayField(TSharedPtr<FJsonObject> JsonObject, const FString& FieldName,
		TArray<TEnum> DefaultEnumArray = TArray<TEnum>())
	{
		if (!JsonObject.IsValid())
			return DefaultEnumArray;
		TArray<TEnum> OutArray = DefaultEnumArray;

		for (FString Str : AwGetStringArrayField(JsonObject, FieldName))
			OutArray.Add(UDataFuncLib::FStringToEnum<TEnum>(Str));
		
		return OutArray;
	};
	template<typename TEnum>
	static TEnum AwGetEnumField(TSharedPtr<FJsonObject> JsonObject, const FString& FieldName, TEnum DefaultEnum)
	{
		if (!JsonObject.IsValid())
			return DefaultEnum;

		FString OutString = "";
		if (JsonObject->TryGetStringField(FieldName, OutString))
			return FStringToEnum<TEnum>(OutString);

		int OutNumber = 0;
		if (JsonObject->TryGetNumberField(FieldName, OutNumber))
			return static_cast<TEnum>(OutNumber);

		return DefaultEnum;
	};
	static bool AwGetBoolField(TSharedPtr<FJsonObject> JsonObject, const FString& FieldName, bool DefaultBool = false);
	static TArray<TSharedPtr<FJsonValue>> AwGetArrayField(TSharedPtr<FJsonObject> JsonObject, const FString& FieldName,
		TArray<TSharedPtr<FJsonValue>> DefaultArray = TArray<TSharedPtr<FJsonValue>>());
	// static FJsonObject AwGetObjectField(TSharedPtr<FJsonObject> JsonObject, const FString& FieldName, FJsonObject DefaultJsonObj = nullptr);
};
