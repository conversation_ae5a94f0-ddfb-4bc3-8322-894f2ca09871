// Fill out your copyright notice in the Description page of Project Settings.


#include "GetEquippmentHint.h"

#include "Engine/Texture2D.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void UGetEquippmentHint::NativeOnInitialized()
{
	Super::NativeOnInitialized();
	EquippmentGroundCanvasPanel = Cast<UCanvasPanel>(GetWidgetFromName("EquippmentGround"));
	EquippmentImage = Cast<UImage>(GetWidgetFromName("Equippment"));
	EquippmentNameTextBlock = Cast<UTextBlock>(GetWidgetFromName("EquippmentName"));
	
}

void UGetEquippmentHint::NativeConstruct()
{
	Super::NativeConstruct();
	Show();
}

void UGetEquippmentHint::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);

	/*if (UGameplayFuncLib::GetAwGameState()->MyCharacter->IsActionOccur("Newbie_Confirm", EAwInputState::Press, true))
	{
		Hide();
	}*/
	
}

void UGetEquippmentHint::Set(FString Id) const
{
	if(EquippmentImage)
	{
		const FString Str = TEXT("Get_") + Id;
		const FString IconPath = UGameplayFuncLib::GetAwDataManager()->GetItemIconById(Str).Path;
		UTexture2D* EquipmentTexture2D = LoadObject<UTexture2D>(nullptr,
			*UResourceFuncLib::GetAssetPath(IconPath));
		EquippmentImage->SetBrushFromTexture(EquipmentTexture2D);
	}
	if(EquippmentNameTextBlock)
		EquippmentNameTextBlock->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey(Id)));
}


void UGetEquippmentHint::Show()
{
	//WasState = UGameplayFuncLib::GetMyAwPlayerController()->GameControlState;
	/*UGameplayFuncLib::GetMyAwPlayerController()->GameControlState = EGameControlState::NewbieHint;
	UGameplayFuncLib::PauseGameActors(TArray<AAwCharacter*>());*/
}

void UGetEquippmentHint::Hide()
{
	//UGameplayFuncLib::GetMyAwPlayerController()->GameControlState = EGameControlState::Game;
	UGameplayFuncLib::ResumeGameActors();
	this->RemoveFromParent();
}
