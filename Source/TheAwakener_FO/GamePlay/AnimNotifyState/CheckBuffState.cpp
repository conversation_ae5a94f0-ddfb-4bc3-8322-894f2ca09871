// Fill out your copyright notice in the Description page of Project Settings.


#include "CheckBuffState.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UCheckBuffState::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyEnd(MeshComp, Animation, EventReference);
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me || BuffId.IsEmpty()) return;
	int BuffNum = Me->GetBuff(BuffId, TArray<AAwCharacter*>()).Num();
	if(BuffNum <= 0 && !NotHasBuffToSection.IsNone())
	{
		UAnimMontage* Montage = Me->GetCurrentActiveMontage();
		FAnimMontageInstance* MontageInstance = Me->GetActiveMontageInstance();
		if (Montage && MontageInstance)
		{
			const int32 SectionID = Montage->GetSectionIndex(NotHasBuffToSection);
			float StartTime = 0.f;
			float EndTime = 0.f;
			Montage->GetSectionStartAndEndTime(SectionID, StartTime, EndTime);
			MontageInstance->SetPosition(StartTime); 
		}
	}
}

void UCheckBuffState::NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyTick(MeshComp, Animation, FrameDeltaTime, EventReference);
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me || BuffId.IsEmpty()) return;
	int BuffNum = Me->GetBuff(BuffId, TArray<AAwCharacter*>()).Num();
	if(BuffNum > 0 && !HasBuffToSection.IsNone())
	{
		UAnimMontage* Montage = Me->GetCurrentActiveMontage();
		FAnimMontageInstance* MontageInstance = Me->GetActiveMontageInstance();
		if (Montage && MontageInstance)
		{
			const int32 SectionID = Montage->GetSectionIndex(HasBuffToSection);
			float StartTime = 0.f;
			float EndTime = 0.f;
			Montage->GetSectionStartAndEndTime(SectionID, StartTime, EndTime);
			MontageInstance->SetPosition(StartTime); 
		}
	}
}