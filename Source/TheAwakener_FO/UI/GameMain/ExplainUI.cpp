// Fill out your copyright notice in the Description page of Project Settings.


#include "ExplainUI.h"

#include "Engine/Texture2D.h"

void UExplainUI::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	Explain_Icon			=	Cast<UImage>(GetWidgetFromName("ExplainIcon"));
	Explain_Name			=	Cast<UTextBlock>(GetWidgetFromName("ExplainName"));
	Explain_Describe		=	Cast<UTextBlock>(GetWidgetFromName("ExplainDescribe"));
	Count_TextBlock			=	Cast<UTextBlock>(GetWidgetFromName("Count"));
}

void UExplainUI::NativeConstruct()
{
	Super::NativeConstruct();
}

void UExplainUI::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
}

void UExplainUI::SetAttribute(UTexture2D* Icon,FString Name,FString Describe,int Count)
{
	if(Icon)
	{
		Explain_Icon->SetBrushFromTexture(Icon);
		Explain_Icon->SetDesiredSizeOverride(FVector2D(Icon->GetSizeX(),Icon->GetSizeY()));
	}
	Explain_Name->SetText(FText::FromString(Name));
	Explain_Describe->SetText(FText::FromString(Describe));
	const FString Temp = FString::FromInt(Count);
	Count_TextBlock->SetText(FText::FromString(Temp));
}
