// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/SceneComponent.h"
#include "ShoppingGoodsPoint.generated.h"

/**
 * 小摊贩卖的东西摆放在小摊贩的Actor上的点
 */
UCLASS(ClassGroup="Collision", editinlinenew, hidecategories=(Object,LOD,Lighting,TextureStreaming), meta=(DisplayName="Shopping Goods Point", BlueprintSpawnableComponent))
class THEAWAKENER_FO_API UShoppingGoodsPoint : public USceneComponent
{
	GENERATED_BODY()
public:
	//这个挂点属于商店的第几个Deal，要是有重复的就糟糕了，所以出bug了首先可以走这里查询
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int Index = 0;

	//在这个点按上键，会跳转到几号下标的UShoppingGoodsPoint，如果找不到对应下标也会产生不跳转
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int GotoIndexWhileUp = 0;
	//在这个点按下键，会跳转到几号下标的UShoppingGoodsPoint，如果找不到对应下标也会产生不跳转
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int GotoIndexWhileDown = 0;
	//在这个点按左键，会跳转到几号下标的UShoppingGoodsPoint，如果找不到对应下标也会产生不跳转
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int GotoIndexWhileLeft = 0;
	//在这个点按右键，会跳转到几号下标的UShoppingGoodsPoint，如果找不到对应下标也会产生不跳转
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int GotoIndexWhileRight = 0;
};
