// Fill out your copyright notice in the Description page of Project Settings.


#include "DefenseActive.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TheAwakener_FO/GamePlay/Roguelike/BattleUpgrade/RogueBattleUpgradeSubSystem.h"

void UDefenseActive::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
                                 const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyBegin(MeshComp, Animation, TotalDuration, EventReference);
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;

	NewDefense = this->Defense;
	
	if (AffectAllCharacterHitBoxes == true)
		NewDefense.CharacterHitBoxId = Me->AllActiveCharacterHitBoxIds();
	NewDefense.AttackerActionChange.HitStun.AutoSetActive();
	NewDefense.DefenderActionChange.HitStun.AutoSetActive();
	
	Me->AddDefenseInfo(NewDefense);
}


void UDefenseActive::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyEnd(MeshComp, Animation, EventReference);
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	const UAnimMontage* Mont = Cast<UAnimMontage>(Animation);
	const FAnimMontageInstance* MontageInstance = Mont ? Me->GetAwAnimInstance()->GetActiveInstanceForMontage(Mont) : nullptr;
	const bool Playing = MontageInstance ? MontageInstance->IsPlaying() : false;
	if (Mont && MontageInstance && Playing == false) return;
	Me->RemoveDefenseInfo(NewDefense);
}
