// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "Particles/ParticleSystemComponent.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "BeamEffect.generated.h"

// 持续多少时间的粒子Comp
// 因为 NotifyState 里面删除粒子会不成功，所以用这个结构存放在 GameState 里面计时去删除。
USTRUCT(BlueprintType)
struct FDurationParticleComp
{
	GENERATED_BODY()

public:
	// 计时器
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float Timer = 0;

	// 持续时间
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float Duration = 0;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool IdDeactivate = false;
		
	// 粒子
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	UParticleSystemComponent* ThisParticle = nullptr;
};

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UBeamEffect : public UAnimNotifyState
{
	GENERATED_BODY()

private:
	// Cached version of the Rotation Offset already in Quat form
	FQuat RotationOffsetQuat;
	UPROPERTY()
	UParticleSystemComponent* TheParticle;
public:

	UBeamEffect();

	// Begin UObject interface
	virtual void PostLoad() override;
#if WITH_EDITOR
	virtual void PostEditChangeProperty(struct FPropertyChangedEvent& PropertyChangedEvent) override;
#endif
	// End UObject interface
	
	virtual void NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
		const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
		const FAnimNotifyEventReference& EventReference) override;
	
	// Particle System to Spawn
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="AnimNotify", meta=(DisplayName="Particle System"))
	UParticleSystem* PSTemplate;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="AnimNotify")
	FVector BeamEndOffSet = FVector::ZeroVector;
	
	// 运行得到 BeamEndPoint 的点
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="AnimNotify")
	FJsonFuncData GetBeamEndPointFunc;
	
	// Location offset from the socket
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="AnimNotify")
	FVector LocationOffset;

	// Rotation offset from socket
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="AnimNotify")
	FRotator RotationOffset;

	// Scale to spawn the particle system at
	UPROPERTY(EditAnywhere, Category="AnimNotify")
	FVector Scale;

	// Should attach to the bone/socket
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="AnimNotify")
	uint32 Attached:1; 	//~ Does not follow coding standard due to redirection from BP

	// SocketName to attach to
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AnimNotify")
	FName SocketName;

	
};
