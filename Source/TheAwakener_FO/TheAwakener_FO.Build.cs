// Copyright Epic Games, Inc. All Rights Reserved.

using System.IO;
using UnrealBuildTool;

public class TheAwakener_FO : ModuleRules
{
	public TheAwakener_FO(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;
		PublicDependencyModuleNames.AddRange(new string[] { "Core", "CoreUObject", "Engine", "InputCore", "UMG", "Json",
			"AIModule", "LevelSequence","MovieScene", "GameplayCameras","XeSSBlueprint","DLSSBlueprint","ApplicationCore","ApparatusRuntime", "ApparatistRuntime", "BlueprintJson", "DeveloperSettings"});

		PrivateDependencyModuleNames.AddRange(new string[] {"CustomMeshComponent", "NavigationSystem", "MediaAssets",
			"Slate", "SlateCore", "Niagara"
		});
		// Add editor dependencies for MonsterSetupTool
		if (Target.bBuildEditor)
		{
			PrivateDependencyModuleNames.AddRange(new string[] {
				"UnrealEd",
				"ComponentVisualizers",
				"AssetTools",
				"EditorStyle",
				"EditorWidgets",
				"ToolMenus"
			});
		}
		RuntimeDependencies.Add(Path.Combine(ModuleDirectory, "../../Config", "DefaultGameUserSettings.ini"));
		
		PublicDependencyModuleNames.AddRange(new string[]{"OnlineSubsystem","onlineSubsystemUtils","Steamworks"});
		// Uncomment if you are using Slate UI
		PrivateDependencyModuleNames.AddRange(new string[] { "Slate", "SlateCore" });
		
		// Uncomment if you are using online features
		PrivateDependencyModuleNames.Add("OnlineSubsystem");

		// To include OnlineSubsystemSteam, add it to the plugins section in your uproject file with the Enabled attribute set to true
		DynamicallyLoadedModuleNames.Add("OnlineSubsystemSteam");
		
	}
}
