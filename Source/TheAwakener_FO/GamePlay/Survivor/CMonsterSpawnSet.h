#pragma once
#include "Dom/JsonObject.h"
#include "CoreMinimal.h"
#include "CMonsterSpawnSet.generated.h"
USTRUCT(BlueprintType)
struct FCMonsterSpawnSet
{
	GENERATED_BODY()
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString MobId;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString AlterId;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float RandomWeight;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int MobLevel;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int Availability;
	static FCMonsterSpawnSet FromJson(TSharedPtr<FJsonObject> JsonObj);
};