// Fill out your copyright notice in the Description page of Project Settings.


#include "MontageAnimPickFunc.h"

#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"


int UMontageAnimPickFunc::Random(AAwCharacter* Character, FActionParam ActionParam, TArray<FString> Params)
{
	const int IntMax = FCString::Atoi(*Params[0]);
	return FMath::RandRange(0, IntMax);
}

int UMontageAnimPickFunc::GetAnimByMoveSpeed(AAwCharacter* Character, FActionParam ActionParam, TArray<FString> Params)
{
	//UKismetSystemLibrary::PrintString(Character, FString("In side"));
	const float MoveSpeed = Character->GetMoveSpeed();
	return FMath::CeilToInt(MoveSpeed / 300);
}

int UMontageAnimPickFunc::GetAnimByOnGround(AAwCharacter* Character, FActionParam ActionParam, TArray<FString> Params)
{
	return Character->OnGround() == true ? 0 : 1;
}

int UMontageAnimPickFunc::PowerSourceFrontBehind(AAwCharacter* Character, FActionParam ActionParam, TArray<FString> Params)
{
	const float DirV = FVector::DotProduct(
		Character->GetActorForwardVector().GetSafeNormal(),
		Character->LastIncomingForce.GetSafeNormal()
	);

	const FForceMoveInfo ForceMoveInfo = Character->GetMoveComponent()->GetForceMove();
	const FAnimFreezeInfo FreezeInfo = Character->GetAwAnimInstance()->GetFreezeInfo();
	
	if(Character->CurrentActionState() == ECharacterActionState::Falling ||
		(ForceMoveInfo.Active && ForceMoveInfo.Velocity.Z > 0) ||
		(FreezeInfo.Active && FreezeInfo.MovePlan.Velocity.Z > 0))
		return DirV <= 0 ? 2 : 3;
	else
		return DirV <= 0 ? 0 : 1;
}

int UMontageAnimPickFunc::PowerSourceForBlow(AAwCharacter* Character, FActionParam ActionParam, TArray<FString> Params)
{
	const FVector FlyVec = Character->LastIncomingForce.GetSafeNormal();
	const float XYSize = FVector2D(FlyVec.X, FlyVec.Y).Size();
	if (XYSize < FlyVec.Z / 2.000f)
	{
		if(Character->CurrentActionState() == ECharacterActionState::Falling)
			return 3;
		return 0;	
	}
	const float DirV = FVector::DotProduct(
			Character->GetActorForwardVector().GetSafeNormal(),
			Character->LastIncomingForce.GetSafeNormal()
		);
	
	if (Character->CurrentActionState() == ECharacterActionState::Falling)
		return DirV <= 0 ? 4 : 5;
	else
		return DirV <= 0 ? 1 : 2;
}

int UMontageAnimPickFunc::GetActionByPriorityDistance(AAwCharacter* Character, FActionParam ActionParam, TArray<FString> Params)
{
	int CurRank = Params.Num() > 0 ? FCString::Atoi(*Params[0]) : 2;
	if (CurRank <= 0) CurRank = 2;
	int Res = FMath::FloorToInt(FMath::Max(0, ActionParam.PriorityDistance)*1.f / CurRank);
	UKismetSystemLibrary::PrintString(Character, FString("Priority Distance = ").Append(FString::FromInt(Res)).Append(">>").Append(FString::FromInt(CurRank)));
	return Res;
}

int UMontageAnimPickFunc::GetBuffStack(AAwCharacter* Character, FActionParam ActionParam, TArray<FString> Params)
{
	if (Params.Num() < 0) return 0;
	const FString BuffId = Params[0];
	TArray<FBuffObj*> Buffs = Character->GetBuff(BuffId, TArray<AAwCharacter*>());
	int Res = 0;
	for (const FBuffObj* Buff : Buffs)
	{
		Res += Buff->Stack;
	}
	return Res;
}

int UMontageAnimPickFunc::CheckFightingWillLevel(AAwCharacter* Character, FActionParam ActionParam, TArray<FString> Params)
{
	if (Params.Num() <= 0) return 0;
	const int CheckLevel = FCString::Atoi(*Params[0]);
	if(Character->FightingWill.Level < CheckLevel)
		return 0;
	else
		return 1;
}

int UMontageAnimPickFunc::ReturnFightingWillLevel(AAwCharacter* Character, FActionParam ActionParam, TArray<FString> Params)
{
	return Character->FightingWill.Level;
}
//貌似没有在使用，如果以后用到了就加沿用自动获取id号的代码
int UMontageAnimPickFunc::ReturnPawnClassIndex(AAwCharacter* Character, FActionParam ActionParam,
	TArray<FString> Params)
{
	UAwDataManager* DataManager = UGameplayFuncLib::GetDataManager();
	TArray<FString> characterKeys;
	int index = 0;
	for (const auto& RoguePawn : DataManager->RoleCreation.RoguePawns)
	{
		characterKeys.Add(RoguePawn.Id);
		if (RoguePawn.Id == Character->CharacterObj.ClassId)
			break;
		index++;
	}

	if (Character->CharacterObj.ClassId == "Swordsman_Gerasso")
		return 0;
	else if (Character->CharacterObj.ClassId == "BladeDancer_Henrik")
		return 1;
	else if (Character->CharacterObj.ClassId == "Spearman_Sola")
		return 2;
	else if (Character->CharacterObj.ClassId == "Warrior_Tierdagon")
		return 3;
	else if (Character->CharacterObj.ClassId == "Warrior_Caelynn")
		return 4;
	else if (Character->CharacterObj.ClassId == "Monk_Wukong")
		return 5;
	return 0;
}
