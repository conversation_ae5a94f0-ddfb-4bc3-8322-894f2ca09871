// Fill out your copyright notice in the Description page of Project Settings.


#include "CheckBuffAnimNotifyState_PlayParticleEffect.h"

#include "Components/SkeletalMeshComponent.h"
#include "Particles/ParticleSystemComponent.h"
#include "TheAwakener_FO/FunctionLibrary/CommonFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"




void UCheckBuffAnimNotifyState_PlayParticleEffect::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
                                            float TotalDuration, const FAnimNotifyEventReference& EventReference)
{
	AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Character) return;
	// ensure deprecated path is called because a call to <PERSON> is not made
	PRAGMA_DISABLE_DEPRECATION_WARNINGS
	Super::NotifyBegin(MeshComp, Animation, TotalDuration);
	PRAGMA_ENABLE_DEPRECATION_WARNINGS
	
	if (!ParticleParameterData&&ValidateParameters(MeshComp))
	{
		UParticleSystemComponent* NewComponent = UGameplayStatics::SpawnEmitterAttached(PSTemplate, MeshComp, SocketName, LocationOffset, RotationOffset, EAttachLocation::KeepRelativeOffset, !bDestroyAtEnd);
	}
	else
	{
		for (auto Tag : CheckBuffTags)
		{
			bool bUseParentNotify = false;
			for (auto Buff : Character->CharacterObj.Buff)
			{
				if (Buff.Model.Tags.Contains(Tag))
				{
					if (AbilityIdCheck.Num() == 0)
					{
						bUseParentNotify = true;
						break;
					}
					else
					{
						if (UCommonFuncLib::ArrayContainsArray(Buff.Model.Tags, AbilityIdCheck, false))
						{
							bUseParentNotify = true;
							break;
						}
					}
				}
			}
			if (bUseParentNotify)
			{
				FName RowName = FName(Tag);
				FAnimStatePlayParticleEffectData* Data  = ParticleParameterData->FindRow<FAnimStatePlayParticleEffectData>(RowName,nullptr);
				if (Data)
				{
					PSTemplate = Data->PSTemplate;
					LocationOffset = Data->LocationOffset;
					RotationOffset = Data->RotationOffset;
					bDestroyAtEnd = Data ->bDestroyAtEnd;
					SocketName = Data->SocketName;
				}
				if (ValidateParameters(MeshComp))
				{
					UParticleSystemComponent* NewComponent = UGameplayStatics::SpawnEmitterAttached(PSTemplate, MeshComp, SocketName, LocationOffset, RotationOffset, EAttachLocation::KeepRelativeOffset, !bDestroyAtEnd);
					FXChildren.Add(NewComponent);
				}
			}
		}
	}

	Received_NotifyBegin(MeshComp, Animation, TotalDuration, EventReference);
}

void UCheckBuffAnimNotifyState_PlayParticleEffect::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	PRAGMA_DISABLE_DEPRECATION_WARNINGS
	Super::NotifyEnd(MeshComp, Animation);
	PRAGMA_ENABLE_DEPRECATION_WARNINGS
	AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Character) return;
	if (!ParticleParameterData)
	{
		DestroyParticleOnEnd(MeshComp,Animation,EventReference);
	}
	else
	{
		for (auto Temp:FXChildren)
		{
			if (IsValid(Temp.Get())&&!Temp->bWasDeactivated)
			{
				if(bDestroyAtEnd)
				{
					Temp->DestroyComponent();
				}
				else
				{
					Temp->DeactivateSystem();
				}
			}
		}
		FXChildren.Empty();
		/*
		for (auto Tag : CheckBuffTags)
		{
			if (bUseParentNotify)
			{
				FName RowName = FName(Tag);
				FAnimStatePlayParticleEffectData* Data  = ParticleParameterData->FindRow<FAnimStatePlayParticleEffectData>(RowName,nullptr);
				if (Data)
				{
					PSTemplate = Data->PSTemplate;
					LocationOffset = Data->LocationOffset;
					RotationOffset = Data->RotationOffset;
					bDestroyAtEnd = Data ->bDestroyAtEnd;
					SocketName = Data->SocketName;
				}
				DestroyParticleOnEnd(MeshComp,Animation,EventReference);
			}
		}
		*/
	}

	Received_NotifyEnd(MeshComp, Animation, EventReference);
}

void UCheckBuffAnimNotifyState_PlayParticleEffect::DestroyParticleOnEnd(USkeletalMeshComponent * MeshComp, UAnimSequenceBase * Animation, const FAnimNotifyEventReference& EventReference)
{
	TArray<USceneComponent*> Children;
	MeshComp->GetChildrenComponents(false, Children);
	
	for(USceneComponent* Component : Children)
	{
		if(UParticleSystemComponent* ParticleComponent = Cast<UParticleSystemComponent>(Component))
		{
			bool bSocketMatch = ParticleComponent->GetAttachSocketName() == SocketName;
			bool bTemplateMatch = ParticleComponent->Template == PSTemplate;

#if WITH_EDITORONLY_DATA
			// In editor someone might have changed our parameters while we're ticking; so check 
			// previous known parameters too.
			bSocketMatch |= PreviousSocketNames.Contains(ParticleComponent->GetAttachSocketName());
			bTemplateMatch |= PreviousPSTemplates.Contains(ParticleComponent->Template);
#endif

			if(bSocketMatch && bTemplateMatch && !ParticleComponent->bWasDeactivated)
			{
				// Either destroy the component or deactivate it to have it's active particles finish.
				// The component will auto destroy once all particle are gone.
				if(bDestroyAtEnd)
				{
					ParticleComponent->DestroyComponent();
				}
				else
				{
					ParticleComponent->DeactivateSystem();
				}

#if WITH_EDITORONLY_DATA
				// No longer need to track previous values as we've found our component
				// and removed it.
				PreviousPSTemplates.Empty();
				PreviousSocketNames.Empty();
#endif
				// Removed a component, no need to continue
				break;
			}
		}
	}
}
