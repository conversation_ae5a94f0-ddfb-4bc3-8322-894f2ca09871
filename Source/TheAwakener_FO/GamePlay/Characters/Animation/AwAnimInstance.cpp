// Fill out your copyright notice in the Description page of Project Settings.


#include "AwAnimInstance.h"

#include "Components/SkeletalMeshComponent.h"
#include "Kismet/KismetGuidLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/ResourceFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

FAnimFreezeInfo UAwAnimInstance::GetFreezeInfo() const
{
	return FreezeInfo;
}

void UAwAnimInstance::NativeUpdateAnimation(float DeltaSeconds)
{
	Super::NativeUpdateAnimation(DeltaSeconds);
}

void UAwAnimInstance::Tick(float DeltaTime)
{
	TickLerp(ForwardSpeed, TargetForwardSpeed, ValueLerpSpeed, DeltaTime);
	TickLerp(Sideways, TargetSideways, ValueLerpSpeed, DeltaTime);
	
	//计算当前动作动画速率百分比
	AAwCharacter* OwnerCharacter = Cast<AAwCharacter>(GetOwningActor());
	if (IsValid(OwnerCharacter))
	{
		OwnerCharacter->GetMesh()->GlobalAnimRateScale = OwnerCharacter->GetActionSpeedRate();
	}
	
	if (this->FreezeInfo.Active)
	{
		UE_LOG(LogTemp,Log,TEXT("FreezingTime: %f - %f by %s"),this->FreezeInfo.FreezingTime,DeltaTime,*GetName());
		if (this->FreezeInfo.FreezingTime <= 0)
			TerminateFreeze();
		this->FreezeInfo.FreezingTime -= DeltaTime;
	}
	else
	{
		FreezeInfo.FreezingPassed += DeltaTime;
	}

	if (!GetCurrentActiveMontage()&&	!FreezeInfo.Active)
	{
		CurMontageUID.Empty();
	}
	if (IsValid(this->Character))
		InAiming = this->Character->InAiming() || this->Character->GetCmdComponent()->IsHoldingAim();
	else
		InAiming = false;
}

bool UAwAnimInstance::InFreezing() const
{
	return this->FreezeInfo.Active;
}

void UAwAnimInstance::TerminateFreeze()
{
	if (FreezeInfo.Active)
	{
		FreezeInfo.FreezingTime = 0;
		bIsFreezing = false;
		Montage_Resume(FreezeInfo.FreezingMontage);
		Character->AddForceMove(FreezeInfo.MovePlan);
		FreezeInfo.MovePlan.Type = EForceMoveType::KnockOut;
		FreezeInfo.MovePlan.Active = false;
		FreezeInfo.MovePlan.Velocity = FVector::ZeroVector;
		FreezeInfo.Active = false;
		FreezeInfo.FreezingPassed = 0;
	}
}


void UAwAnimInstance::PlayMoveBlendSpace(FString AnimationPath)
{
	FMoveBlendSpace MoveBs;
	MoveBs.MoveBlendSpace = Cast<UBlendSpace>(UResourceFuncLib::LoadAnimAsset(AnimationPath));
	if (UResourceFuncLib::ExistAsset<UAnimationAsset>(AnimationPath+"_Start"))
	{
		MoveBs.HasStart = true;
		MoveBs.MoveBlendSpace_Start = Cast<UBlendSpace>(UResourceFuncLib::LoadAnimAsset(AnimationPath+"_Start"));
	}
	else
	{
		MoveBs.HasStart = false;
		MoveBs.MoveBlendSpace_Start = MoveBs.MoveBlendSpace;
	}
	if (UResourceFuncLib::ExistAsset<UAnimationAsset>(AnimationPath+"_End"))
	{
		MoveBs.HasEnd = true;
		MoveBs.MoveBlendSpace_End = Cast<UBlendSpace>(UResourceFuncLib::LoadAnimAsset(AnimationPath+"_End"));
	}
	else
	{
		MoveBs.HasEnd = false;
		MoveBs.MoveBlendSpace_End = MoveBs.MoveBlendSpace;
	}

	if (IsPlayMoveBlendSpaceA && MoveBs_A == MoveBs ||
		!IsPlayMoveBlendSpaceA && MoveBs_B == MoveBs)
			return;

	if (IsPlayMoveBlendSpaceA)
	{
		MoveBs_B = MoveBs;
		IsPlayMoveBlendSpaceA = false;
	}
	else
	{
		MoveBs_A = MoveBs;
		IsPlayMoveBlendSpaceA = true;
	}
	
	// HasStart = UResourceFuncLib::ExistAsset<UAnimationAsset>(AnimationPath+"_Start");
	// if (HasStart)
	// 	MoveBlendSpace_Start = Cast<UBlendSpace>(UResourceFuncLib::LoadAnimAsset(AnimationPath+"_Start"));
	// else
	// 	MoveBlendSpace_Start = MoveBlendSpace;
	//
	// HasEnd = UResourceFuncLib::ExistAsset<UAnimationAsset>(AnimationPath+"_End");
	// if (HasEnd)
	// 	MoveBlendSpace_End = Cast<UBlendSpace>(UResourceFuncLib::LoadAnimAsset(AnimationPath+"_End"));
	// else
	// 	MoveBlendSpace_End = MoveBlendSpace;
	
	bIsPlayAimBlendSpace = false;
}

void UAwAnimInstance::PlayAimBlendSpace(int Index)
{
	if (UActionComponent* ActionComp = Character->GetActionComponent())
	{
		switch (ActionComp->GetCharacterActionState())
		{
		case ECharacterActionState::Ground:
		case ECharacterActionState::Falling:
			{
				UAwDataManager* DataManager = UGameplayFuncLib::GetDataManager();
				const FString ClassId = Character->CharacterObj.ClassId;
				FBattleClassModel BattleClassModel = DataManager->GetBattleClassModelById(ClassId);
				const FString AnimPath = BattleClassModel.AimBlendSpace[Index];
			
				AimBlendSpace = Cast<UBlendSpace>(UResourceFuncLib::LoadAnimAsset(AnimPath));
				if (IsValid(AimBlendSpace))
					bIsPlayAimBlendSpace = true;
			
				break;
			}
		case ECharacterActionState::Flying:
		case ECharacterActionState::Attached:
		default:
			{
				StopAimBlendSpace();
				break;
			}
		}
	}
	else
		StopAimBlendSpace();
}

void UAwAnimInstance::StopAimBlendSpace()
{
	bIsPlayAimBlendSpace = false;
}

bool UAwAnimInstance::PlayActionMontage(FString MontagePath, FActionPlanInfo ActionPlanInfo)
{
	// if (Character->IsPlayerCharacter())
	// {
	// 	UKismetSystemLibrary::PrintString(this,MontagePath);
	// }
	if (Character)
	{
		UAnimationAsset* ToPlay = UResourceFuncLib::LoadAnimAsset(MontagePath);
		const UAnimMontage* CurPAnim = Character->GetCurrentActiveMontage();
		//如果当前没有动画在播放 或者 要播放的是受伤类型的动画 或者 要播放的动画和现在的不同，那么就播放
		if (!CurPAnim || Character->IsHurtAction(ActionPlanInfo.ActionInfo) || ToPlay->GetFullName() != CurPAnim->GetFullName())
		{
			UAnimMontage* WillMontage = Cast<UAnimMontage>(ToPlay);
			const float MontageLength = Montage_Play(WillMontage, 1.f, EMontagePlayReturnType::MontageLength, ActionPlanInfo.StartFrom);
			if (WillMontage) WillMontage->RateScale = 1;	//强行设定回到1
			bIsPlayAimBlendSpace = false;
			CurMontageUID = UKismetGuidLibrary::NewGuid().ToString();
			// const int AnimIndex = ActionPlanInfo.ActionInfo.Anim.AnimPath.IndexOfByKey(MontagePath);
			// if (AnimIndex >= 0 && ActionPlanInfo.ActionInfo.Anim.CanStopSprint.Num() > AnimIndex)
			// 	if (ActionPlanInfo.ActionInfo.Anim.CanStopSprint[AnimIndex])
			// 		Character->GetCmdComponent()->IsSprint = false;
			// else
			// 	Character->GetCmdComponent()->IsSprint = false;
				
			if (ActionPlanInfo.ActionInfo.CanStopSprint)
				Character->GetCmdComponent()->IsSprint = false;

			// 首帧无敌
			if (ActionPlanInfo.ActionInfo.IsInvincibleOnFirstFrame)
				Character->SetAllCharacterHitBoxActive(false);
			
			const  bool PlayFail = FMath::IsNearlyZero(MontageLength);
			if (!PlayFail)
			{
				UE_LOG(LogTemp,Log,TEXT("Plan Freeze for %f seconds. by %s"),ActionPlanInfo.FreezeTime,*GetName());
				FreezeAnim(ActionPlanInfo.FreezeTime, ActionPlanInfo.MoveAfterFrozen);
			}

			return  PlayFail == false;
		
		}
		
	}
	return false;
}

void UAwAnimInstance::SetAnimForwardSpeed(float NewForwardSpeed)
{
	this->TargetForwardSpeed = FMath::Clamp(NewForwardSpeed, -1.f, ForwardSpeedLimit);
	if (NewForwardSpeed != 0)
		StopForwardSpeed = NewForwardSpeed;
}

void UAwAnimInstance::SetAnimForwardSpeedLimit(int Limit)
{
	this->ForwardSpeedLimit = Limit;
}

void UAwAnimInstance::SetAnimSideways(float NewSideways)
{
	this->TargetSideways = FMath::Clamp(NewSideways, -1.f, 1.f);
}

void UAwAnimInstance::TickLerp(float& Value, float TargetValue, float NewValueLerpSpeed, float DeltaSeconds) const
{
	if (FMath::IsNearlyEqual(TargetValue, Value, 0.01f))
	{
		Value = TargetValue;
	}
	else
	{
		const float NewLerpSpeed = NewValueLerpSpeed * DeltaSeconds;
		if (TargetValue > Value)
		{
			Value += NewLerpSpeed;
			if (Value > TargetValue)
				Value = TargetValue;
		}
		else
		{
			Value -= NewLerpSpeed;
			if (Value < TargetValue)
				Value = TargetValue;
		}
	}
}

void UAwAnimInstance::FreezeAnim(float Time, FForceMoveInfo MoveAfterDefrozen)
{
	UAnimMontage* CurMont = GetCurrentActiveMontage();
	// if (FreezeInfo.Active == true)// && FreezeInfo.FreezingMontage != CurMont)
	// {
	// 	UKismetSystemLibrary::PrintString(this, "Resume Paused Montage");
	// 	Montage_Resume(FreezeInfo.FreezingMontage);
	// 	//this->Character->GetMesh()->GlobalAnimRateScale = this->Character->GetActionSpeedRate();
	// }
	if (Time > 0)
	{
		//UKismetSystemLibrary::PrintString(this, FString(this->Character->GetName()).Append(FString("Pause Montage For ")).Append(FString::FromInt(Time * 1000)).Append(" Force Move: ").Append(MoveAfterDefrozen.Velocity.ToString()));
		FreezeInfo.FreezingMontage = CurMont;
		bIsFreezing = true;
		Montage_Pause(CurMont);
		
		//this->Character->GetMesh()->GlobalAnimRateScale = 0.01f;// 0.1f;
		const float ToFreezeTime = FreezeInfo.Active ? (
			 //FMath::Max(FMath::Min((Time + FreezeInfo.FreezingTime) * 0.300f, Time), FreezeInfo.FreezingTime)
			FMath::Max(FMath::Min((Time + FreezeInfo.FreezingTime) * UGameplayFuncLib::GetAwDataManager()->DebugConfig.FreezingRate, Time),
				UGameplayFuncLib::GetAwDataManager()->DebugConfig.MinFreezingTime)
		) : Time;
		FreezeInfo.FreezingTime = ToFreezeTime;	//FreezeInfo.Active ? FMath::Max(Time, FreezeInfo.FreezingTime) : Time;
		FreezeInfo.FreezingPassed = 0;
		UE_LOG(LogTemp,Log,TEXT("UAwAnimInstance::FreezeAnim Time = %f by %s"),ToFreezeTime,*GetName());
		if (MoveAfterDefrozen.Active == true)
		{
			FreezeInfo.MovePlan = MoveAfterDefrozen;
		}else
		{
			FreezeInfo.MovePlan.Active = false;
			FreezeInfo.MovePlan.Velocity = FVector::ZeroVector;
		}
		FreezeInfo.Active = true;
	}
	else
	{
		// TerminateFreeze();
		if (MoveAfterDefrozen.Active)
			Character->AddForceMove(MoveAfterDefrozen);
	}
}

bool UAwAnimInstance::IsInFreezingPassedThreshold()
{
	return UGameplayFuncLib::GetAwDataManager()->DebugConfig.FreezingPassedThreshold >= FreezeInfo.FreezingPassed;
}
