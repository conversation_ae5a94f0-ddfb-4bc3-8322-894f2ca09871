// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "Components/SceneComponent.h"
#include "CoreMinimal.h"
#include "Engine/Classes/Particles/ParticleSystemComponent.h"
#include "PlayingFXOnCharacter.generated.h"

/**
 * 角色身上正在播放的视听信息
 */
USTRUCT()
struct FPlayingFXOnCharacter
{
	GENERATED_BODY()
public:
	//这个播放内容的预约Id
	UPROPERTY()
	FString Id;

	//这个播放内容的视觉特效<绑点, 粒子效果>
	UPROPERTY()
	TMap<USceneComponent*, UParticleSystemComponent*> VFX;

	//视觉特效的路径
	UPROPERTY()
	FString VFXPath;
	
	//优先级，如果角色身上的这种临时特效太多了，就会排序后删除优先级低的
	UPROPERTY()
	int Priority = 0;

	//是否是一个只播放一次的特效
	UPROPERTY()
	bool PlayOnce = false;
	
};
