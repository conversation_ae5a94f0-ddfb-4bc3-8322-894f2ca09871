// Fill out your copyright notice in the Description page of Project Settings.

#pragma once
#include "Animation/AnimMontage.h"

#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/Attack/ForceMoveInfo.h"
#include "AnimFreezeInfo.generated.h"

/**
 * “卡肉”信息
 */
USTRUCT()
struct FAnimFreezeInfo
{
	GENERATED_BODY()
public:
	//是否启动
	UPROPERTY()
	bool Active = false;

	UPROPERTY()
	UAnimMontage* FreezingMontage = nullptr;
	
	//还要卡多久
	UPROPERTY()
	float FreezingTime;

	UPROPERTY()
	float FreezingPassed;

	//卡完之后发生位移
	UPROPERTY()
	FForceMoveInfo MovePlan;

	FAnimFreezeInfo():FreezingTime(0),MovePlan(FForceMoveInfo()){}
	FAnimFreezeInfo(UAnimMontage* FreezeMontage, float FreezeTime, FForceMoveInfo Move = FForceMoveInfo()):
		Active(true), FreezingMontage(FreezeMontage),FreezingTime(FreezeTime), MovePlan(Move){};
};
