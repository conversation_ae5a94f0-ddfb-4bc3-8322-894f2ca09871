// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/SceneComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/ChaPart/EnumChaPart.h"
#include "TheAwakener_FO/GamePlay/Characters/ChaProp/ChaProp.h"
#include "TheAwakener_FO/GamePlay/DamageVolume/DamageValue.h"
#include "TheAwakener_FO/GamePlay/Thing/ThingObj.h"
#include "Equipment.generated.h"

/**
 * 装备的部位类型，装备的分类（头、身体、手、脚、武器）
 */
UENUM(BlueprintType)
enum class EEquipmentPart: uint8
{
	//非法的部位
	Invalid,
	// 头
	Head,
	// 身体
	Body,
	// 手
	Arm,
	// 脚
	Leg,
	//武器
	Weapon,
	//正在使用的道具
	UsingItem
};



/**
 * 职业武器的动作分类，TODO 待干掉
 */
UENUM(BlueprintType)
enum class EClassWeaponType: uint8
{
	// 徒手或者收刀
	UnArmed,
	// 大剑类
	BigSword,
	//双剑
	TwinSword,
	//长柄
	PoleArm, 
	// 弓箭
	Archery,
	//剑盾
	SwordShield,
};

/**
 * 装备外观组件类型
 */
UENUM(BlueprintType)
enum class EEquipmentAppearanceType: uint8
{
	// 普通类型组件，比如武器一般都是，没有动画，不用蒙皮
	Normal,
	//动画类型组件，比如角色装备、弓箭等会跟着角色动画播放的，属于蒙皮组件
	SkinnedMesh,
	//物理组件，需要计算物理的，比如衣服上的飘带、披风、狼皮等
	Physical
};

USTRUCT(BlueprintType)
struct FEquipmentSet
{
	GENERATED_BODY()
	
public:
	FString Id = "";

	FString EquipmentSet = "";

	FString StoneId = "";

	static FEquipmentSet FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FEquipmentSet Res;
		Res.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id");
		Res.EquipmentSet = UDataFuncLib::AwGetStringField(JsonObj, "EquipSet");
		Res.StoneId = UDataFuncLib::AwGetStringField(JsonObj,"StoneId");
		return Res;
	};
};

/**
 *装备外观信息
 */
USTRUCT(BlueprintType)
struct FEquipmentAppearancePart
{
	GENERATED_BODY()
public:	
	//外观模型蓝图文件路径
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString BluePrintPath;

	//绑定的绑点的id
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> BindPointIds;

	//这是一个表达这个视觉部位应该存在哪个slot的东西，这个slot是可以自定义的，但是2个装备的Appearance中有相同PartSlot的，就会被视为这两件装备的显示有冲突
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString PartSlot;

	//这个组件的类别，决定了绑到绑点上之后的做法，SkinMesh，physical等
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EEquipmentAppearanceType Type = EEquipmentAppearanceType::Normal;

	//所属的装备部位
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EEquipmentPart PartType = EEquipmentPart::Body;

	//这个部件的装备的Actor
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> PhysicalBoneName = TArray<FString>();

	//这个部件替换掉身上某些块，仅适用于Type == SkinnedMesh的部位
	//取值范围：Head Body LeftArm RightArm LeftLeg RightLeg
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> HideBodyParts;

	//这个部位的显示优先级，在冲突时，优先级高的留下
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int Priority = 0;

	//这个部位冲突的PartSlot
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> ConflictPartSlots;

	//耐久度>=这个值的时候显示这个部位
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int ShowAboveDurability = 1;

	//耐久度<=这个值的时候显示这个部位
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int ShowBelowDurability = 999999;

	//在哪一种Type的CharacterObj显示这个部位，如果这个列表是空，则会在任何类型上显示
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> ShowOnCharacterType;

	bool operator==(const FEquipmentAppearancePart Other) const
	{
		return this->Type == Other.Type && this->PartSlot == Other.PartSlot &&
			this->BindPointIds == Other.BindPointIds && this->BluePrintPath == Other.BluePrintPath &&
			this->PhysicalBoneName == Other.PhysicalBoneName &&
			this->Priority == Other.Priority &&
			this->ConflictPartSlots == Other.ConflictPartSlots &&
			this->ShowAboveDurability == Other.ShowAboveDurability &&
			this->ShowBelowDurability == Other.ShowBelowDurability;
	}

	static FEquipmentAppearancePart FromJson(TSharedPtr<FJsonObject> JsonObj);
};

/**
 * 装备视觉部位在装备ReduceDurability之后发生的变化
 */
USTRUCT(BlueprintType)
struct FEquipmentDurabilityModifyResult
{
	GENERATED_BODY()
public:
	//装备视觉发生了变化了吗，如果是false，那么其余的都没有意义了
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool Changed = false;

	//变化导致这些绑点的外观被移除（如有必要才改FEquipmentAppearance)
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FEquipmentAppearancePart> RemoveAppearance;

	//变化是否导致解开了新的部位的绑点（如有必要才改FEquipmentAppearance)
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FEquipmentAppearancePart> AddAppearance;

	FEquipmentDurabilityModifyResult(){};
	FEquipmentDurabilityModifyResult(TArray<FEquipmentAppearancePart> RemoveApp, TArray<FEquipmentAppearancePart> AddApp):
		Changed(RemoveApp.Num() > 0 || AddApp.Num() > 0), RemoveAppearance(RemoveApp), AddAppearance(AddApp){};
	FEquipmentDurabilityModifyResult(bool HasChange, TArray<FEquipmentAppearancePart> RemoveApp, TArray<FEquipmentAppearancePart> AddApp):
		Changed(HasChange), RemoveAppearance(RemoveApp), AddAppearance(AddApp){};
};

/**
 * 角色的装备
 */
USTRUCT(BlueprintType)
struct FEquipment
{
	GENERATED_BODY()
public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString UniqueId;
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Id;
	
	//所属的装备部位
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EEquipmentPart PartType = EEquipmentPart::Body;

	//提供的属性
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FChaProp Property;

	//影响的角色部位
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EChaPartType AffectPart = EChaPartType::Body;

	//装备的部件，这些部件是显示用的信息
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FEquipmentAppearancePart> AppearanceParts;

	//部位肉质，其实就是防御力了
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FDamageValue Meat;

	//部位肉质类型被设置为这个值，同样对武器无效
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EChaPartMeatType MeatType = EChaPartMeatType::Meat;

	//当前的耐久度档次，这个档次会影响装备部位的显示性
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int Durability = 1;

	//最大耐久度档次
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int DurabilityMax = 1;

	//要播放的视觉特效相关的Key
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> FXKey;

	/**
	 *装备的Clip，每个Clip的等级，0级肯定就是没有这个Clip了
	 *身体护甲的Clip暂定为0胸甲，1腹部，2左肩膀，3右肩膀
	 *手臂护甲的Clip为0手套，1下臂，2上臂，3肩膀
	 *下身防具为0鞋子，1小腿，2大腿，3裙摆
	 *头盔为0护额（头箍），1盔帽，2护面（脸颊），3面罩
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<int> ClipLevel;
	
	
	//TODO 其他属性再议

	// 词条
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> EquipSets;
	
	/**
	 * 在某个耐久度等级，有哪些部位是要显示的
	 * @param BodyType 角色的类型
	 * @param DurabilityLevel 要检查的耐久度等级
	 * @return 要显示的装备的指针
	 */
	TArray<FEquipmentAppearancePart> PartsToShow(FString BodyType, int DurabilityLevel);

	/**
	 * 降低装备的耐久度等级
	 * @param BodyType 角色的类型
	 * @param Value 要降低的量，如果是负数则会增加
	 * @return 等级产生的变化是否值的重新刷一下显示
	 */
	FEquipmentDurabilityModifyResult ReduceDurability(FString BodyType, int Value);

	bool operator ==(const FEquipment& OtherEquipment) const
	{
		return this->Id == OtherEquipment.Id &&
			this->AffectPart == OtherEquipment.AffectPart &&
				this->PartType == OtherEquipment.PartType;
	}

	static FEquipment FromThing(const FThingObj& ThingObj);
	
	static FEquipment FromJson(TSharedPtr<FJsonObject> JsonObj);

	//获取部位对应的称号
	static FString GetPartTypeText(FEquipment Equipment);

	
	//-----------------------合成玩法才有用的-------------------------------------------------
	//根据ClipLevel重新计算得出装备的肉质
	void ResetDataByClips();
	//某个外观部位已经存在外观显示内容
	bool HasAppearancePartInSlot(FString SlotName);
};

/**
 *  装备的绑点，装备只能绑定在这些绑点上
 */
UCLASS(ClassGroup="Collision", editinlinenew, hidecategories=(Object,LOD,Lighting,TextureStreaming), meta=(DisplayName="Equipment Bind Point", BlueprintSpawnableComponent))
class THEAWAKENER_FO_API UEquipmentBindPoint : public USceneComponent
{
	GENERATED_BODY()

};
