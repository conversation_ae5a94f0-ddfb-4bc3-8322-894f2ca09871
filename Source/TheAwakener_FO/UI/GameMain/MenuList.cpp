// Fill out your copyright notice in the Description page of Project Settings.


#include "MenuList.h"

#include "TimerManager.h"
#include "BehaviorTree/BehaviorTreeTypes.h"
#include "Blueprint/WidgetLayoutLibrary.h"
#include "Components/CanvasPanelSlot.h"
#include "Engine/Texture2D.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/ResourceFuncLib.h"


void UMenuList::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	
	ListBackGround = Cast<UImage>(GetWidgetFromName(TEXT("ListGround")));

	MenuList = Cast<UAwListView>(GetWidgetFromName(TEXT("MenuListView")));


	ListState = ListSelect;
	ListStyle = EListStyle::ListLimited;
	ListSizeMax = FVector2D(900.0f,900.0f);
	MenuList->OnItemSelectionChanged.AddDynamic(this, &UMenuList::OnItemSelectionChanged);
	
}

void UMenuList::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
	
}

void UMenuList::RemoveFromParent()
{
	Super::RemoveFromParent();
	
	if(MenuList)
		MenuList->OnItemSelectionChanged.RemoveDynamic(this, &UMenuList::OnItemSelectionChanged);
	
}

void UMenuList::SetEntry(FListItemElementInfo ElementInfos)
{
	SetListAttributes(ElementInfos.ListStyle,ElementInfos.EntrySpace,
			LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(ElementInfos.ListBlackGroundImage)),
			ElementInfos.ListGroundPosition,ElementInfos.ListPosition,
			ElementInfos.ListMaxSize,ElementInfos.ListComValue);
	
	MenuList->ClearListItems();
	if(ElementInfos.Items.Num() > 0)
	{
		UMenuListEntryData* Temp = nullptr;
		for(const FListItems Item : ElementInfos.Items)
		{
			Temp = NewObject<UMenuListEntryData>();
			
			Temp->Id = Item.Id;
			Temp->ItemType = Item.ListItemType;
			
			
			Temp->IconGroundImage = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(Item.IconGroundImage));
			Temp->IconImage = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(Item.IconImage));
			Temp->EntryGroundImage = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(Item.EntryGroundImage));
			Temp->EntryGroundSelectBoxImage= LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(Item.EntrySelectImage));
			Temp->EntryMakerBoxImage = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(Item.MakerImage));
			Temp->EntrySelectBoxImage = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(Item.SelectImage));

			Temp->UniqueId				= Item.UniqueId;
			Temp->Index					= Item.Index;
			Temp->Count					= Item.Count;
			Temp->IsMainHand			= Item.IsMainHand;
			Temp->Name					= Item.Name;
			Temp->IsEquipped			= Item.IsEquipped;
			Temp->ElementState			= Item.ElementState;
			Temp->EntryStyle			= Item.EntryStyle;
			Temp->ItemId				= Item.ItemId;
			Temp->ItemAttributValue		= Item.EquipmentAttributValue;
			Temp->SceneCameraLocation	= Item.SceneCameraLocation;
			Temp->ModelRotator			= Item.ModelRotator;
			Temp->ModelLocation			= Item.ModelLocation;
			Temp->TypeName				= Item.TypeName;
			Temp->Affix					= Item.Affix;
			

			
			Temp->Interval				= ElementInfos.InterValue;
			Temp->CurveAngle			= ElementInfos.CurveAngle;
			Temp->Diameter				= ElementInfos.DisplacementValue;
			Temp->CompensationValue		= ElementInfos.CompensationValue;
			Temp->IsShrink				= ElementInfos.IsShrink;
			
			

			MenuList->AddItem(Temp);
		}
		
		
		SelectData = Cast<UMenuListEntryData>(MenuList->GetItemAt(0));
		MenuList->SetSelectedItem(SelectData);
		MenuList->ScrollIndexIntoView(0);
		SelectData->ElementState = EElementState::State_Select;
		
		
		GetWorld()->GetTimerManager().SetTimer(TimeHandle,this,&UMenuList::SetListSize,0.07f,true);
	}
	
}

void UMenuList::OnItemSelectionChanged(UObject* Item, bool bIsSelected)
{

	if(SelectData && SelectData != Cast<UMenuListEntryData>(Item))
	{
		SelectData->ElementState = EElementState::State_Generally;
		SelectData = Cast<UMenuListEntryData>(Item);
	}
	
}

void UMenuList::SetListAttributes(EListStyle listStyle, float EntrySpace,UTexture2D* ListBackGroundImage,
	FVector2D ListGroundPosition,FVector2D ListPosition,
	FVector2D ListMaxSize,float ListCom)
{
	if(ListBackGroundImage)
	{
		ListBackGround->SetBrushFromTexture(ListBackGroundImage);
		ListBackGround->SetDesiredSizeOverride(FVector2D(ListBackGroundImage->GetSizeX(), ListBackGroundImage->GetSizeY()));
		//ListBackGround->SetBrushSize(FVector2D(ListBackGroundImage->GetSizeX(),ListBackGroundImage->GetSizeY()));
	}
	else
	{
		ListBackGround->SetVisibility(ESlateVisibility::Collapsed);
	}
		

	UWidgetLayoutLibrary::SlotAsCanvasSlot(ListBackGround)->SetPosition(ListGroundPosition);
	UWidgetLayoutLibrary::SlotAsCanvasSlot(MenuList)->SetPosition(ListPosition);
	
	ListStyle = listStyle;

	ListComValue = ListCom;

	ListSizeMax = FVector2D::Min(ListMaxSize, FVector2D(900.0f,900.0f));
	
	//MenuList->SetEntrySpacing(FMath::Clamp(EntrySpace,0.0f,50.0f));
}

void UMenuList::ListSelectUp()
{
	if(ListState == ListSelect)
	{
		ListState = ListSelectChanging;
		
		if(ListStyle == EListStyle::ListEndless && MenuList->GetIndexForItem(MenuList->GetSelectedItem<UMenuListEntryData>()) == 0)
		{
			SelectIndex = MenuList->GetNumItems() - 1;
			
			MenuList->ScrollIndexIntoView(SelectIndex);
			MenuList->SetSelectedIndex(SelectIndex);
			
		}
		else
		{
			if(MenuList->GetIndexForItem(MenuList->GetSelectedItem<UMenuListEntryData>()) == 0)
			{
				CurSelectIsToporDown = true;
			}
			else
			{
				UGameplayFuncLib::PlayUIAudio("SwitchKey_UpDown");
				CurSelectIsToporDown = false;
				SelectIndex = MenuList->GetIndexForItem(MenuList->GetSelectedItem<UMenuListEntryData>());
				SelectIndex -= 1;
				const int Temp = FMath::Clamp(SelectIndex,0,MenuList->GetNumItems() - 1);
				MenuList->ScrollIndexIntoView(Temp);
				MenuList->SetSelectedIndex(Temp);
			}
		}
		
		GetWorld()->GetTimerManager().SetTimer(TimeHandle,this,&UMenuList::SetListState,0.15f,true);
	}
	
}

void UMenuList::ListSelectDown()
{
	if(ListState == ListSelect)
	{
		
		ListState = ListSelectChanging;
		
		if(ListStyle == EListStyle::ListEndless && MenuList->GetIndexForItem(MenuList->GetSelectedItem<UMenuListEntryData>()) == MenuList->GetNumItems() - 1)
		{
			SelectIndex = 0;
			
			MenuList->ScrollIndexIntoView(SelectIndex);
			MenuList->SetSelectedIndex(SelectIndex);
			
		}
		else
		{
			if(MenuList->GetIndexForItem(MenuList->GetSelectedItem<UMenuListEntryData>()) == MenuList->GetNumItems() - 1)
			{
				CurSelectIsToporDown = true;
			}
			else
			{
				UGameplayFuncLib::PlayUIAudio("SwitchKey_UpDown");
				CurSelectIsToporDown = false;
				SelectIndex = MenuList->GetIndexForItem(MenuList->GetSelectedItem<UMenuListEntryData>());
				SelectIndex += 1;
				const int Temp = FMath::Clamp(SelectIndex,0,MenuList->GetNumItems() - 1);
			
				MenuList->ScrollIndexIntoView(Temp);
				MenuList->SetSelectedIndex(Temp);
			}
			
		}
		
		GetWorld()->GetTimerManager().SetTimer(TimeHandle,this,&UMenuList::SetListState,0.15f,true);
	}
	
}

void UMenuList::SetListState()
{
	switch (ListState)
	{
	case ListSelect:
		
		break;
	case ListSelectChanging:
		ListState = ListSelect;
		break;
	}
	GWorld->GetTimerManager().ClearTimer(TimeHandle);
}

void UMenuList::SetListSize()
{
	MenuList->SetScrollbarVisibility(ESlateVisibility::Collapsed);
	float TempPosX = 0.0f;
	float ListSizeX = 0.0f;
	
	
	UMenuListEntry* Test = Cast<UMenuListEntry>(MenuList->GetEntryWidgetFromItem(MenuList->GetItemAt(0)));
	if(Test)
	{
		//const float TempSizeY = (MenuList->GetNumItems() * Test->GetDesiredSize().Y) + (MenuList->GetNumItems() * MenuList->GetEntrySpacing());
		//float ListSizeY = 0;
		//const float TempViewPortY = 1080.0f;
		
		//float ListSizeY = FMath::Min(TempSizeY,ListSizeMax.Y);
		/*if(TempSizeY > TempViewPortY)
		{
			float SizeY = TempSizeY - TempViewPortY;
			int Count = SizeY / (Test->GetDesiredSize().Y + MenuList->GetEntrySpacing()) + 2;
			ListSizeY = ((MenuList->GetNumItems() - Count)  * Test->GetDesiredSize().Y) + ((MenuList->GetNumItems() - Count) * MenuList->GetEntrySpacing());
		}
		else
		{
			ListSizeY = TempSizeY;
		}*/
		
		UMenuListEntry* TempEntry = nullptr;
		for(UObject* Temp : MenuList->GetListItems())
		{
			
			TempEntry = Cast<UMenuListEntry>(MenuList->GetEntryWidgetFromItem(Temp));
			if(TempEntry)
			{
				TempPosX = FMath::Max(TempPosX,UWidgetLayoutLibrary::SlotAsCanvasSlot(TempEntry->MenuList_EntryBase)->GetPosition().X);
				
				ListSizeX = TempEntry->MenuList_EntryBase->GetDesiredSize().X + TempPosX + TempEntry->GetEntryData()->Diameter + ListComValue;
				
			}
			
		}
		
		//UWidgetLayoutLibrary::SlotAsCanvasSlot(MenuList)->SetSize(FVector2D(ListSizeX,ListSizeY));
		GetWorld()->GetTimerManager().ClearTimer(TimeHandle);
	}
}

