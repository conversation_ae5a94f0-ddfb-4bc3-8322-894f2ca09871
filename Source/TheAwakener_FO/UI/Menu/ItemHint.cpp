// Fill out your copyright notice in the Description page of Project Settings.


#include "ItemHint.h"

#include "Engine/Texture2D.h"
#include "Components/Button.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/ResourceFuncLib.h"
#include "TheAwakener_FO/GameFramework/Manager/AwDataManager.h"
#include "TheAwakener_FO/GamePlay/Thing/ThingUIInfo.h"

void UItemHint::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	this->ItemControlTextComp = Cast<UTextBlock>(GetWidgetFromName(TEXT("ItemControlText")));
	this->ItemControlButtonComp = Cast<UButton>(GetWidgetFromName(TEXT("ItemControlButton")));
	this->ItemControllerComp = Cast<UCanvasPanel>(GetWidgetFromName(TEXT("ItemController")));
	this->ItemIconComp = Cast<UImage>(GetWidgetFromName(TEXT("ItemIcon")));
	this->ItemNameComp = Cast<UTextBlock>(GetWidgetFromName(TEXT("ItemName")));
	this->ItemDescComp = Cast<UTextBlock>(GetWidgetFromName(TEXT("ItemDesc")));
	
	this->WeaponNameComp = Cast<UTextBlock>(GetWidgetFromName(TEXT("WeaponName")));
	this->WeaponTypeComp = Cast<UTextBlock>(GetWidgetFromName(TEXT("WeaponType")));
	this->WeaponIconComp = Cast<UImage>(GetWidgetFromName(TEXT("WeaponIcon")));
	this->WeaponStateComp = Cast<UTextBlock>(GetWidgetFromName(TEXT("WeaponState")));
	
	this->EquipmentNameComp = Cast<UTextBlock>(GetWidgetFromName(TEXT("EquipmentName")));
	this->EquipmentTypeComp = Cast<UTextBlock>(GetWidgetFromName(TEXT("EquipmentType")));
	this->EquipmentIconComp = Cast<UImage>(GetWidgetFromName(TEXT("EquipmentIcon")));
	this->EquipmentStateComp = Cast<UTextBlock>(GetWidgetFromName(TEXT("EquipmentState")));
	
	this->MainComp = Cast<UWidgetSwitcher>(GetWidgetFromName(TEXT("Main")));
}

void UItemHint::SetAsEquipment(FEquipment Equipment, FString StateText)
{
	const FThingUIInfo ShowUI = FThingUIInfo::AsEquipment(-1, Equipment.Id);
	this->MainComp->SetActiveWidgetIndex(0);
	this->EquipmentNameComp->SetText(FText::FromString(ShowUI.Name));
	UTexture2D* IconTexture = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(ShowUI.Icon));
	if (IconTexture) this->EquipmentIconComp->SetBrushFromTexture(IconTexture);
	EquipmentTypeComp->SetText(FText::FromString(FEquipment::GetPartTypeText(Equipment)));
	EquipmentStateComp->SetText(FText::FromString(StateText));
}

void UItemHint::SetAsWeapon(FEquippedWeaponSet WeaponObj, FString StateText)
{
	const FThingUIInfo ShowUI = FThingUIInfo::AsWeaponObj(-1, WeaponObj.WeaponType);
	this->MainComp->SetActiveWidgetIndex(1);
	this->WeaponNameComp->SetText(FText::FromString(ShowUI.Name));
	UTexture2D* IconTexture = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(ShowUI.Icon));
	if (IconTexture) this->WeaponIconComp->SetBrushFromTexture(IconTexture);
	WeaponTypeComp->SetText(FText::FromString(FEquippedWeaponSet::GetWeaponTypeText(WeaponObj)));
	WeaponStateComp->SetText(FText::FromString(StateText));
}

void UItemHint::SetAsItemObj(FItemObj Item, bool ShowControlComp, FString ControlCompText)
{
	const FThingUIInfo ShowUI = FThingUIInfo::AsItem(-1, Item.Model.Id);
	this->MainComp->SetActiveWidgetIndex(2);
	this->ItemNameComp->SetText(FText::FromString(ShowUI.Name));
	UTexture2D* IconTexture = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(ShowUI.Icon));
	if (IconTexture) this->ItemIconComp->SetBrushFromTexture(IconTexture);
	if (ShowControlComp)
	{
		this->ItemControllerComp->SetVisibility(ESlateVisibility::Visible);
		this->ItemControlTextComp->SetText(FText::FromString(ControlCompText));
	}else
	{
		this->ItemControllerComp->SetVisibility(ESlateVisibility::Hidden);
	}
	this->ItemDescComp->SetText(FText::FromString(ShowUI.Description));
}

void UItemHint::SetAsShopDeal(FDeal Deal, FString ButtonText)
{
	this->MainComp->SetActiveWidgetIndex(2);
	this->ItemNameComp->SetText(FText::FromString(Deal.Name));
	UTexture2D* IconTexture = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(Deal.Icon));
	if (IconTexture) this->ItemIconComp->SetBrushFromTexture(IconTexture);
	this->ItemDescComp->SetText(FText::FromString(Deal.Description));
	
	this->ItemControllerComp->SetVisibility(ESlateVisibility::Visible);
	this->ItemControlTextComp->SetText(FText::FromString(ButtonText));
	
}