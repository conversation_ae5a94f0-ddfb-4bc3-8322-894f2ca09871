// Fill out your copyright notice in the Description page of Project Settings.


#include "RemoveBuff.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void URemoveBuff::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);
	
	AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Character) return;
	FBuffModel BuffModel = UGameplayFuncLib::GetDataManager()->GetBuffModelById(this->BuffId);
	if(BuffModel.Id != "")
	{
		//FAddBuffInfo BuffInfo = FAddBuffInfo(Character, Character, BuffModel, BuffModel.MaxStack * -1, 1, true, false);
		//Character->AddBuff(BuffInfo);
		Character->RemoveBuffById(this->BuffId);
	}
}

