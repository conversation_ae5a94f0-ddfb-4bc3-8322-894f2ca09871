// Fill out your copyright notice in the Description page of Project Settings.


#include "AwObject.h"

#include "Engine/Engine.h"
#include "Engine/GameInstance.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"

UGameInstance* UAwObject::GetGameInstance()
{
	UGameInstance* GameInstance = nullptr;

	if (GetWorld() != nullptr && GetWorld()->GetGameInstance() != nullptr)
		GameInstance = UGameplayStatics::GetGameInstance(GetWorld());
	else if (IsValid(GameInstance))
		GameInstance = GEngine->GetWorld()->GetGameInstance();
	else
		UE_LOG(LogTemp, Error, TEXT("Game Instance cannot be obtained"));
	
	return GameInstance;
}
