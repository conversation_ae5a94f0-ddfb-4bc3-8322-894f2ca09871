// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "Dom/JsonObject.h"
#include "CoreMinimal.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "ChaProp.generated.h"

//角色的潜能属性的类别，转化为现有属性的时候会根据类别有不同的公式
UENUM(BlueprintType)
enum class EChaPotentialType : uint8
{
	PlayerCharacter,	//是一个玩家角色
	NormalMob,		//普通怪物
	EliteMob,		//精英怪物
	Boss,			//Boss级
	Weapon,	//武器上属性
	Armor,		//防具上属性
	Buff,			//用于Buff
};


/**
 * 角色的现有值
 */
USTRUCT(BlueprintType)
struct FChaResource
{
	GENERATED_BODY()
public:
	//角色的现有生命值
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int HP = 0;

	//角色的现有法力值
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int MP = 0;

	//角色的现有体力值
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int SP = 0;

	//角色的现有觉醒点
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int AP = 0;

	//空中闪避点
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int AirDodgePoint = 0;
	
	FChaResource(){};
	FChaResource(int Hp, int Mp = 0, int Sp = 0,int Ap =0,int AirDPoint = 0):HP(Hp),MP(Mp),SP(Sp),AP(Ap),AirDodgePoint(AirDPoint){};

	static FChaResource FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		return FChaResource(
			UDataFuncLib::AwGetNumberField(JsonObj, "HP", 0),
			UDataFuncLib::AwGetNumberField(JsonObj, "MP", 0),
			UDataFuncLib::AwGetNumberField(JsonObj, "SP", 0),
			UDataFuncLib::AwGetNumberField(JsonObj, "Ap", 0),
			UDataFuncLib::AwGetNumberField(JsonObj, "AirDPoint", 0)
		);
	}

	/**
	 * 是否足够
	 * @param Condition 要对比的量
	 * @return 是否足够Condition需求的量
	 */
	bool Enough(FChaResource Condition) const
	{
		return this->HP >= Condition.HP &&
			   this->MP >= Condition.MP &&
			   this->SP >= Condition.SP &&
			   this->AP >= Condition.AP &&
			   this->AirDodgePoint >= Condition.AirDodgePoint;
	}

	FChaResource operator +(const FChaResource& Other) const
	{
		return FChaResource(
			this->HP + Other.HP,
			this->MP + Other.MP,
			this->SP + Other.SP,
			this->AP + Other.AP,
			this->AirDodgePoint + Other.AirDodgePoint
		);
	}
	FChaResource operator -(const FChaResource& Other) const
	{
		return FChaResource(
			this->HP - Other.HP,
			this->MP - Other.MP,
			this->SP - Other.SP,
			this->AP - Other.AP,
			this->AirDodgePoint - Other.AirDodgePoint
		);
	}

	void MinValueForCharacter()
	{
		this->HP = FMath::Clamp(this->HP ,0.F,this->HP );
		this->MP = FMath::Clamp(this->	MP ,0.F,this->MP );
		this->SP = FMath::Clamp(this->SP ,0.F,this->SP );
		this->AP = FMath::Clamp(this->AP ,0.F,this->AP );
		this->AirDodgePoint = FMath::Clamp(this->AirDodgePoint, 0.f, this->AirDodgePoint);
	}
};

/**
 * 角色战斗属性
 */
USTRUCT(BlueprintType)
struct FChaProp
{
	GENERATED_BODY()

public:
	//生命值上限
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int HP = 0;

	//法力值上限
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int MP = 0;
	//体力上限
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int SP = 0;
	//觉醒点上限
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int AP = 0;

	//空中闪避点上限
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int AirDodgePoint = 0;
	
	//物理攻击力
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int PAttack = 0;

	//魔法攻击力
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int MAttack = 0;

	//物理防御力
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int PDefense = 0;

	//魔法防御力
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int MDefense = 0;
	
	//移动速度
	UPROPERTY()
	TArray<int> MoveSpeed;

	//行动速度  1=1%
	UPROPERTY(BlueprintReadOnly)
	int ActionSpeed = 0;

	//被击飞倍率
	UPROPERTY(BlueprintReadOnly)
	float BeStrikeRate = 0.00f;

	//暴击率
	UPROPERTY(BlueprintReadOnly)
	float CriticalChance = 0.f;

	//暴击伤害倍率
	UPROPERTY(BlueprintReadOnly)
	float CriticalRate = 0.f;
	
	UPROPERTY(BlueprintReadOnly)
	float HPRestore = 0.f;
	UPROPERTY(BlueprintReadOnly)
	float MPRestore = 0.f;

	FChaProp()
	{
		this->MoveSpeed.Add(0);
		this->MoveSpeed.Add(0);
		this->MoveSpeed.Add(0);
	}
	
	void MinValueForCharacter()
	{
		this->HP = FMath::Max(1, this->HP);
		this->MP = FMath::Max(0, this->MP);
		this->SP = FMath::Max(0, this->SP);
		this->AP = FMath::Max(0, this->AP);
		this->AirDodgePoint = FMath::Max(0, AirDodgePoint);
		
		this->PAttack = FMath::Max(0, this->PAttack);
		this->MAttack = FMath::Max(0, this->MAttack);
		this->PDefense = FMath::Max(0, this->PDefense);
		this->MDefense = FMath::Max(0, this->MDefense);
		
		this->MoveSpeed[0] = FMath::Max(0, this->MoveSpeed[0]);
		this->MoveSpeed[1] = FMath::Max(0, this->MoveSpeed[1]);
		this->MoveSpeed[2] = FMath::Max(0, this->MoveSpeed[2]);
		
		this->ActionSpeed = FMath::Max(0, this->ActionSpeed);
		this->BeStrikeRate = FMath::Max(1.000f, this->BeStrikeRate);
		this->CriticalChance = FMath::Max(0,this->CriticalChance);
		this->CriticalRate = FMath::Max(1.f,this->CriticalRate);
		
		this->HPRestore = FMath::Max(0.f, this->HPRestore);
		this->MPRestore = FMath::Max(0.f, this->MPRestore);			
	}

	static FChaProp PotentialFromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FChaProp EvaluationReport = FChaProp();

		EvaluationReport.HP = UDataFuncLib::AwGetNumberField(JsonObj, "Hp", 0);
		EvaluationReport.MP = UDataFuncLib::AwGetNumberField(JsonObj, "Mp", 0);
		EvaluationReport.SP = UDataFuncLib::AwGetNumberField(JsonObj, "Sp", 0);
		EvaluationReport.AP = UDataFuncLib::AwGetNumberField(JsonObj, "Ap", 0);
		EvaluationReport.AirDodgePoint = UDataFuncLib::AwGetNumberField(JsonObj, "AirDodgePoint", 0);
		EvaluationReport.PAttack = UDataFuncLib::AwGetNumberField(JsonObj, "PAtk", 0);
		EvaluationReport.MAttack = UDataFuncLib::AwGetNumberField(JsonObj, "MAtk", 0);
		EvaluationReport.PDefense = UDataFuncLib::AwGetNumberField(JsonObj, "PDef", 0);
		EvaluationReport.MDefense = UDataFuncLib::AwGetNumberField(JsonObj, "MDef", 0);
		EvaluationReport.BeStrikeRate = UDataFuncLib::AwGetNumberField(JsonObj, "BeStrikeRate", 0.0f);
		EvaluationReport.ActionSpeed = UDataFuncLib::AwGetNumberField(JsonObj, "ActionSpeed", 0);

		EvaluationReport.CriticalChance = UDataFuncLib::AwGetNumberField(JsonObj, "CriticalChance", 0.0f);
		EvaluationReport.CriticalRate = UDataFuncLib::AwGetNumberField(JsonObj, "CriticalRate", 0.f);
		
		int i = 0;
		for (const TSharedPtr<FJsonValue> ArrayField : UDataFuncLib::AwGetArrayField(JsonObj, "MoveSpeed"))
		{
			EvaluationReport.MoveSpeed[i] = ArrayField->AsNumber();
			i++;
		}
		
		EvaluationReport.HPRestore = UDataFuncLib::AwGetNumberField(JsonObj, "HpRestore", 0.0f);
		EvaluationReport.MPRestore = UDataFuncLib::AwGetNumberField(JsonObj, "MpRestore", 0.0f);
	
		return EvaluationReport;
	};

	static FChaProp FromJson(TSharedPtr<FJsonObject> JsonObj, int Level, EChaPotentialType Type)
	{
		FChaProp EvaluationReport = FChaProp();

		EvaluationReport.HP = UDataFuncLib::AwGetNumberField(JsonObj, "Hp", 0);
		EvaluationReport.MP = UDataFuncLib::AwGetNumberField(JsonObj, "Mp", 0);
		EvaluationReport.SP = UDataFuncLib::AwGetNumberField(JsonObj, "Sp", 0);
		EvaluationReport.AP = UDataFuncLib::AwGetNumberField(JsonObj, "Ap", 0);
		EvaluationReport.AirDodgePoint = UDataFuncLib::AwGetNumberField(JsonObj, "AirDodgePoint", 0);
		EvaluationReport.PAttack = UDataFuncLib::AwGetNumberField(JsonObj, "PAtk", 0);
		EvaluationReport.MAttack = UDataFuncLib::AwGetNumberField(JsonObj, "MAtk", 0);
		EvaluationReport.PDefense = UDataFuncLib::AwGetNumberField(JsonObj, "PDef", 0);
		EvaluationReport.MDefense = UDataFuncLib::AwGetNumberField(JsonObj, "MDef", 0);
		EvaluationReport.BeStrikeRate = UDataFuncLib::AwGetNumberField(JsonObj, "BeStrikeRate", 0.0f);
		EvaluationReport.ActionSpeed = UDataFuncLib::AwGetNumberField(JsonObj, "ActionSpeed", 0);

		EvaluationReport.CriticalChance = UDataFuncLib::AwGetNumberField(JsonObj, "CriticalChance", 0.0f);
		EvaluationReport.CriticalRate = UDataFuncLib::AwGetNumberField(JsonObj, "CriticalRate", 0.0f);
		
		int i = 0;
		for (const TSharedPtr<FJsonValue> ArrayField : UDataFuncLib::AwGetArrayField(JsonObj, "MoveSpeed"))
		{
			EvaluationReport.MoveSpeed[i] = ArrayField->AsNumber();
			i++;
		}
		
		EvaluationReport.HPRestore = UDataFuncLib::AwGetNumberField(JsonObj, "HpRestore", 0.0f);
		EvaluationReport.MPRestore = UDataFuncLib::AwGetNumberField(JsonObj, "MpRestore", 0.0f);
	
		return GetChaPropByStar(EvaluationReport, Level, Type);
	};

	/**
	 * 星级换取属性的基本算法
	 * 磨砂猫版公式
	 */
	static int GetBasicResult(int Star, int Level, float Times = 1.000f)
	{
		return FMath::RoundToInt((100.000f + FMath::Pow(Star / 30.000f + Level / 30.000f,2 + Level / 30.000f) * 30.000f) * Times);
	}

	static int GetRogueMobHp(int Star, int Level)
	{
		const float FloatStar = Star;
		const float FloatLevel = Level;
		return FMath::RoundToInt(20.0f + FMath::Pow(FloatStar/1.5f + FloatLevel/1.5f + 1, 2 + FloatStar/50.0f));
	}

	static int GetRogueMobAtk(int Star, int Level)
	{
		// return FMath::RoundToInt(5.0f + Star*Level / (Star+Level+50.0f) * 3.0f);
		const float FloatStar = Star;
		const float FloatLevel = Level;
		return FMath::RoundToInt(5.0f + (FloatStar * FloatLevel) / (FloatStar + FloatLevel + 50) * 4);
	}
	
	/**
	 * 根据属性的潜能，来获得一个最终值
	 * @param Star 潜能ChaProperty
	 * @param Level 当时角色的等级，如果是装备类就无效了
	 * @param Type 当做什么类型的属性来算，比如boss还是小怪
	 * @return The final property for character.
	 */
	static FChaProp GetChaPropByStar(FChaProp Star, int Level, EChaPotentialType Type)
	{
		if (Type == EChaPotentialType::Buff || Type == EChaPotentialType::Weapon || Type == EChaPotentialType::Armor)
				return Star;	//这些值等于星级
		FChaProp Prop = FChaProp();

		//TODO: 需要进一步设计
		float TypeTimes = 1.500f;
		switch (Type)
		{
		case EChaPotentialType::NormalMob: TypeTimes = 1.500f;break;
		case EChaPotentialType::PlayerCharacter:TypeTimes = 3.500f;break;
		case EChaPotentialType::EliteMob: TypeTimes = 9.000f;break;
		case EChaPotentialType::Boss:TypeTimes = 30.000f;break;
		case EChaPotentialType::Buff:
		case EChaPotentialType::Weapon:
		case EChaPotentialType::Armor:
			default:TypeTimes = 1.000f;break;
		}
		Prop.HP = GetBasicResult(Star.HP, Level, TypeTimes);
		// Prop.MP = GetBasicResult(Star.MP, Level, 1.000f);
		Prop.MP = 10000;
		Prop.SP = 10000;
		Prop.AirDodgePoint = 0;
		Prop.PAttack = Type != EChaPotentialType::PlayerCharacter ? (GetBasicResult(Star.PAttack, Level, 0.350f) + 10) : 0;
		Prop.MAttack = Type != EChaPotentialType::PlayerCharacter ? (GetBasicResult(Star.MAttack, Level, 0.350f) + 10) : 0;
		Prop.PDefense = 0;	//防御力应该都是0，玩家靠装备，怪物靠肉质。//Type != EChaPotentialType::PlayerCharacter ? (GetBasicResult(Star.PDefense, Level, 0.350f) + 10) : 0;
		Prop.MDefense = 0; //防御力应该都是0，玩家靠装备，怪物靠肉质。//Type != EChaPotentialType::PlayerCharacter ? (GetBasicResult(Star.MDefense, Level, 0.350f) + 10) : 0;
		Prop.BeStrikeRate = Star.BeStrikeRate;	//这个直接相等
		Prop.ActionSpeed = 0;
		Prop.MoveSpeed = Star.MoveSpeed;	//这个是直接相等
		Prop.HPRestore = Star.HPRestore;	//这个也是直接相等（？）
		Prop.MPRestore = Star.MPRestore;	//这个还是直接相等（？）

		return Prop;
	}

	/**
	 * 根据MDefense算出一个减伤率，也就是肉质
	 * 所以这个值=1.0f的时候受到100%伤害，如果等于0.1就是受到10%伤害
	 */
	float GetPhysicalMeatByPDefense() const
	{
		return 1.000f - (this->PDefense * 1.000f / (this->PDefense + 45.000f));	//最大减伤率0.85，所以是+45
	}

	FChaProp operator+(const FChaProp& OtherProperty) const
	{
		FChaProp Res = FChaProp();
		Res.HP = this->HP + OtherProperty.HP;
		Res.MP = this->MP + OtherProperty.MP;
		Res.SP = this->SP + OtherProperty.SP;
		Res.AP = this->AP + OtherProperty.AP;
		Res.AirDodgePoint = this->AirDodgePoint + OtherProperty.AirDodgePoint;
		Res.PAttack = this->PAttack + OtherProperty.PAttack;
		Res.MAttack = this->MAttack + OtherProperty.MAttack;
		Res.PDefense = this->PDefense + OtherProperty.PDefense;
		Res.MDefense = this->MDefense + OtherProperty.MDefense;
		Res.BeStrikeRate = this->BeStrikeRate + OtherProperty.BeStrikeRate;
		Res.ActionSpeed = this->ActionSpeed + OtherProperty.ActionSpeed;
		Res.CriticalChance = this->CriticalChance + OtherProperty.CriticalChance;
		Res.CriticalRate = this->CriticalRate + OtherProperty.CriticalRate;
		
		Res.MoveSpeed[0] = this->MoveSpeed[0] + OtherProperty.MoveSpeed[0];
		Res.MoveSpeed[1] = this->MoveSpeed[1] + OtherProperty.MoveSpeed[1];
		Res.MoveSpeed[2] = this->MoveSpeed[2] + OtherProperty.MoveSpeed[2];
		
		Res.HPRestore = this->HPRestore + OtherProperty.HPRestore;
		Res.MPRestore = this->MPRestore + OtherProperty.MPRestore;
		return Res;
	}
	FChaProp operator-(const FChaProp& OtherProperty) const
	{
		FChaProp Res = FChaProp();
		Res.HP = this->HP - OtherProperty.HP;
		Res.MP = this->MP - OtherProperty.MP;
		Res.SP = this->SP - OtherProperty.SP;
		Res.AP = this->AP - OtherProperty.AP;
		Res.AirDodgePoint = this->AirDodgePoint - OtherProperty.AirDodgePoint;
		Res.PAttack = this->PAttack - OtherProperty.PAttack;
		Res.MAttack = this->MAttack - OtherProperty.MAttack;
		Res.PDefense = this->PDefense - OtherProperty.PDefense;
		Res.MDefense = this->MDefense - OtherProperty.MDefense;
		Res.BeStrikeRate = this->BeStrikeRate - OtherProperty.BeStrikeRate;
		Res.ActionSpeed = this->ActionSpeed - OtherProperty.ActionSpeed;

		Res.CriticalChance = this->CriticalChance - OtherProperty.CriticalChance;
		Res.CriticalRate = this->CriticalRate - OtherProperty.CriticalRate;
		
		Res.MoveSpeed[0] = this->MoveSpeed[0] - OtherProperty.MoveSpeed[0];
		Res.MoveSpeed[1] = this->MoveSpeed[1] - OtherProperty.MoveSpeed[1];
		Res.MoveSpeed[2] = this->MoveSpeed[2] - OtherProperty.MoveSpeed[2];
		
		Res.HPRestore = this->HPRestore - OtherProperty.HPRestore;
		Res.MPRestore = this->MPRestore - OtherProperty.MPRestore;
		return Res;
	}
	
	FChaProp operator*(const float Times) const
	{
		FChaProp Res = FChaProp();
		Res.HP = FMath::CeilToInt(this->HP * Times); 
		Res.MP = FMath::CeilToInt(this->MP * Times);
		Res.SP = FMath::CeilToInt(this->SP * Times);
		Res.AP = FMath::CeilToInt(this->AP * Times);
		Res.AirDodgePoint = FMath::CeilToInt(this->AirDodgePoint * Times);
		Res.PAttack = FMath::CeilToInt(this->PAttack * Times); 
		Res.MAttack = FMath::CeilToInt(this->MAttack * Times); 
		Res.PDefense = FMath::CeilToInt(this->PDefense * Times);
		Res.MDefense = FMath::CeilToInt(this->MDefense * Times); 
		Res.BeStrikeRate = this->BeStrikeRate * Times;
		Res.ActionSpeed = FMath::CeilToInt(this->ActionSpeed * Times);

		Res.CriticalChance = this->CriticalChance * Times;
		Res.CriticalRate = this->CriticalRate * Times;
		
		Res.MoveSpeed[0] = FMath::CeilToInt(this->MoveSpeed[0] * Times);
		Res.MoveSpeed[1] = FMath::CeilToInt(this->MoveSpeed[1] * Times);
		Res.MoveSpeed[2] = FMath::CeilToInt(this->MoveSpeed[2] * Times);
		
		Res.HPRestore = (this->HPRestore * Times); 
		Res.MPRestore = (this->MPRestore * Times);
		return Res;
	}

	FChaProp operator*(const FChaProp& OtherProperty) const
	{
		//This means OtherProperty value 10000 is 100%.
		constexpr  float PropertyTimesValue = 10000.00000f;	
		FChaProp Res = FChaProp();
		Res.HP = FMath::CeilToInt(this->HP * (PropertyTimesValue + OtherProperty.HP) / PropertyTimesValue);
		Res.MP = FMath::CeilToInt(this->MP * (PropertyTimesValue + OtherProperty.MP) / PropertyTimesValue);
		Res.SP = FMath::CeilToInt(this->SP * (PropertyTimesValue + OtherProperty.SP) / PropertyTimesValue);
		Res.AP = FMath::CeilToInt(this->AP * (PropertyTimesValue + OtherProperty.AP) / PropertyTimesValue);
		Res.AirDodgePoint = FMath::CeilToInt(this->AirDodgePoint * (PropertyTimesValue + OtherProperty.AirDodgePoint) / PropertyTimesValue);
		Res.PAttack = FMath::CeilToInt(this->PAttack * (PropertyTimesValue + OtherProperty.PAttack) / PropertyTimesValue);
		Res.MAttack = FMath::CeilToInt(this->MAttack * (PropertyTimesValue + OtherProperty.MAttack) / PropertyTimesValue);
		Res.PDefense = FMath::CeilToInt(this->PDefense * (PropertyTimesValue + OtherProperty.PDefense) / PropertyTimesValue);
		Res.MDefense = FMath::CeilToInt(this->MDefense * (PropertyTimesValue + OtherProperty.MDefense) / PropertyTimesValue);
		Res.BeStrikeRate = this->BeStrikeRate * (PropertyTimesValue + OtherProperty.BeStrikeRate) / PropertyTimesValue;
		Res.ActionSpeed = FMath::CeilToInt(this->ActionSpeed * (PropertyTimesValue + OtherProperty.ActionSpeed) / PropertyTimesValue);

		Res.CriticalChance = this->CriticalChance * (PropertyTimesValue + OtherProperty.CriticalChance) / PropertyTimesValue;
		Res.CriticalRate = this->CriticalRate * (PropertyTimesValue + OtherProperty.CriticalRate) / PropertyTimesValue;
		
		Res.MoveSpeed[0] = FMath::CeilToInt(this->MoveSpeed[0] * (PropertyTimesValue + OtherProperty.MoveSpeed[0]) / PropertyTimesValue);
		Res.MoveSpeed[1] = FMath::CeilToInt(this->MoveSpeed[1] * (PropertyTimesValue + OtherProperty.MoveSpeed[1]) / PropertyTimesValue);
		Res.MoveSpeed[2] = FMath::CeilToInt(this->MoveSpeed[2] * (PropertyTimesValue + OtherProperty.MoveSpeed[2]) / PropertyTimesValue);
		
		Res.HPRestore = this->HPRestore * (PropertyTimesValue + OtherProperty.HPRestore) / PropertyTimesValue;
		Res.MPRestore = this->MPRestore * (PropertyTimesValue + OtherProperty.MPRestore) / PropertyTimesValue;
		return Res;
	}

	void ResetToZero()
	{
		this->HP = 0;
		this->MP = 0;
		this->SP = 0;
		this->AP = 0;
		this->AirDodgePoint = 0;
		this->PAttack = 0;
		this->MAttack = 0;
		this->PDefense = 0;
		this->MDefense = 0;
		
		this->MoveSpeed.Empty();
		this->MoveSpeed.Add(0);
		this->MoveSpeed.Add(0);
		this->MoveSpeed.Add(0);
		
		this->ActionSpeed = 0;
		this->BeStrikeRate = 0;
		this->CriticalChance =0;
		this->CriticalRate = 0;
		this->HPRestore = 0;
		this->MPRestore =0;
	}

	//资源上限是否不同
	bool PropResEqual(const FChaProp& OtherProperty)const
	{
		const bool bHP = OtherProperty.HP == this->HP;
		const bool bMP = OtherProperty.MP == this->MP;
		const bool bSP = OtherProperty.SP == this->SP;
		const bool bAP = OtherProperty.AP == this->AP;
		const bool bAirDodgePoint = OtherProperty.AirDodgePoint == this->AirDodgePoint;
		return  bHP && bMP && bSP && bAP && bAirDodgePoint;
	}
};
