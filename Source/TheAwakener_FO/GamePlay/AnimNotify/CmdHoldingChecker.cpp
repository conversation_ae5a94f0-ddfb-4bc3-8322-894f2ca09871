// Fill out your copyright notice in the Description page of Project Settings.


#include "CmdHoldingChecker.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"


void UCmdHoldingChecker::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);
	
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	
	const FActionInfo* Action = Me->CurrentAction() ;
	if (Action == nullptr) return;

	//判断按键是否通过了
	TArray<TTuple<FString, EAwInputState>> CheckInput;
	for (FString Cmd : Action->Commands)
	{
		CheckInput.Add(TTuple<FString, EAwInputState>(Cmd, EAwInputState::Hold));
	}

	if (CheckInput.Num() <= 0 || Me->AnyActionOccur(CheckInput) == true)
	{
		//通过检测
		UKismetSystemLibrary::PrintString(Me, FString("Holding Key !!"));
	}else
	{
		//没有通过，就会跳转到指定位置
		UAnimMontage* Montage = Me->GetCurrentActiveMontage();
		FAnimMontageInstance* MontageInstance = Me->GetActiveMontageInstance();
		if (Montage && MontageInstance)
		{
			//Montage->JumpToSectionName(GoToSectionName[GoIndex]);
			const int32 SectionID = Montage->GetSectionIndex(GotoSection);
			if (SectionID != INDEX_NONE)
			{
				float StartTime = 0.f;
				float EndTime = 0.f;
				Montage->GetSectionStartAndEndTime(SectionID, StartTime, EndTime);
				MontageInstance->SetPosition(StartTime); 
			}
		}
	}
}
