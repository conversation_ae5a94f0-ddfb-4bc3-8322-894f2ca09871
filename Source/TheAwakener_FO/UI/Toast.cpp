// Fill out your copyright notice in the Description page of Project Settings.


#include "Toast.h"
#include "Engine/Texture2D.h"
#include "Components/CanvasPanelSlot.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void UToast::NativeOnInitialized()
{
	Super::NativeOnInitialized();
	ToastPanel = Cast<UCanvasPanel>(GetWidgetFromName(TEXT("Toast")));
	ToastBkgImage = Cast<UImage>(GetWidgetFromName(TEXT("ToastBkg")));
	this->PanelSlot = Cast<UCanvasPanelSlot>(ToastPanel->Slot);
	this->TextBlock = Cast<UTextBlock>(GetWidgetFromName(TEXT("ToastText")));
	this->SetVisibility(ESlateVisibility::Hidden);
}

void UToast::NativeConstruct()
{
	Super::NativeConstruct();
}

void UToast::Show(FString Text, float Y)
{
	Key = Text;
	UTexture2D* Temp = nullptr;
	if(Text == "Get_Blacksmith_Tool")
	{
		Temp = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(UGameplayFuncLib::GetAwDataManager()->
			GetItemIconById("BlacksmithTool").Path));
	}
	else if(Text == "Get_Elevator_Stick")
	{
		Temp = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(UGameplayFuncLib::GetAwDataManager()->
			GetItemIconById("LiftHandle").Path));
	}
	if(Temp)
		ToastBkgImage->SetBrushFromTexture(Temp);
	
	Text = UGameplayFuncLib::GetAwDataManager()->GetTextByKey(Text);
	//PanelSlot->SetPosition(FVector2D(0, 0));
	TextBlock->SetText(FText::FromString(Text));
	this->SetVisibility(ESlateVisibility::HitTestInvisible);
	
	/*TargetY = Y;
	Arrived = false;
	StayTime = FMath::Clamp(Text.Len() * 0.05f, ExistenceTimeMin, ExistenceTimeMax);
	ToastPanel->SetRenderOpacity(1);*/
}

void UToast::Hide()
{
	UGameplayFuncLib::ResumeGameActors();
	this->RemoveFromParent();
}


void UToast::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
	
	/*const float Dis = TargetY - PanelSlot->GetPosition().Y;
	const float MoveThisTick = FMath::IsNearlyZero(Dis) ? 999999 : (InDeltaTime * 3000 * (Dis / FMath::Abs(Dis)));

	if (Arrived == true)
	{
		if (StayTime > 0)
		{
			StayTime -= InDeltaTime;
		}else
		{
			if(FMath::IsNearlyEqual(ToastPanel->GetRenderOpacity(),0.0f,0.01f))
			{
				this->RemoveFromParent();
			}
			else
			{
				ToastPanel->SetRenderOpacity(FMath::FInterpTo(ToastPanel->GetRenderOpacity(),0,InDeltaTime,HideSpeed));
			}
		}
	}else
	{
		if (FMath::Abs(Dis) <= FMath::Abs(MoveThisTick))
		{
			PanelSlot->SetPosition(FVector2D(PanelSlot->GetPosition().X, TargetY));
			Arrived = true;
		}else
		{
			PanelSlot->SetPosition(FVector2D(PanelSlot->GetPosition().X, PanelSlot->GetPosition().Y + MoveThisTick));
		}
	}*/
}
