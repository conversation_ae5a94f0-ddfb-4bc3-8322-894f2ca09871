// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "Dom/JsonObject.h"
#include "CoreMinimal.h"
#include "RoleCreation.generated.h"

USTRUCT(BlueprintType)
struct FRoleType
{
	GENERATED_BODY()

public:
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	FString Id = "";

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	FString Desc = "";

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	FString Sex = "Male";

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	FString BpPath = "Core/Characters/StandardHuman/Human_Player_Man";

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	FString DLCIdSteam = "4018760";
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	FString DLCIdStove = "";
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	FString DLCIdPlaystaion = "";

	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	FString FolderName = "Male";

	TArray<FString> Equipments;
	FString MainHandId;
	FString OffHandId;
	int HP;
	int Attack;
	float BreakRatio;
	FString DefaultBattleStyle;
	
	FRoleType(){};
	
	static FRoleType FromJson(TSharedPtr<FJsonObject> JsonObj);
};

/**
 * 账号Role创建信息
 */
USTRUCT(BlueprintType)
struct FRoleCreation
{
	GENERATED_BODY()

public:
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	TArray<FRoleType> Types = TArray<FRoleType>();
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	TArray<FRoleType> RoguePawns = TArray<FRoleType>();

	FRoleCreation(){};

	static FRoleCreation FromJson(TSharedPtr<FJsonObject> JsonObj);
};
