// Fill out your copyright notice in the Description page of Project Settings.


#include "InputAcceptanceModifier.h"

#include "Engine/World.h"
#include "TheAwakener_FO/FunctionLibrary/CallFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"

float FInputAcceptanceModifier::Update(float DeltaTime, bool AllowOverValue)
{
	if (Active == false || Duration <= 0) return EndTimes;
	this->TimeElapsed += DeltaTime;
	
	if (TimeElapsed >= Duration) Active = false;	//只关闭而不暂停本次运算

	float Times = TimeElapsed / Duration;
	if (AllowOverValue == false) Times = FMath::Clamp(Times, 0.f, 1.f);
	if (EaseFunc.IsEmpty()) return Times * (EndTimes - StartTimes) + StartTimes;
	
	const FJsonFuncData FuncData = UCallFuncLib::StringToJsonFuncData(EaseFunc);
	UFunction* Func = UCallFuncLib::JsonFuncToUFunc(FuncData);
	if (!Func || !GWorld) return Times * (EndTimes - StartTimes) + StartTimes;
	
	struct
	{
		float TimePassed;
		float StartSpeedTimes;
		float EndSpeedTimes;
		float Duration;
		TArray<FString> Params;
		float Result;
	}FuncParam;
	FuncParam.TimePassed = TimeElapsed;
	FuncParam.StartSpeedTimes = StartTimes;
	FuncParam.EndSpeedTimes = EndTimes;
	FuncParam.Duration = Duration;
	FuncParam.Params = FuncData.Params;
	UObject* TempObject = NewObject<UObject>();
	if (TempObject)
	{
		TempObject->ProcessEvent(Func, &FuncParam);
	}
	return FuncParam.Result;
}
