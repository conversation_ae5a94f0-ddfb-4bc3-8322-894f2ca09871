// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "Dom/JsonObject.h"
#include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/Elemental/Elemental.h"
#include "TheAwakener_FO/GamePlay/TerrainAndWeather/Terrain.h"
#include "TheAwakener_FO/GamePlay/TerrainAndWeather/Weather.h"
#include "ElementalTalent.generated.h"

/**
 * 元素效果触发时间点
 */
UENUM(BlueprintType)
enum class EElementalTriggerType  : uint8
{
	OnHit,	//迸发，在命中的时候条件激活，首先动作（ActionInfo）允许，其次是OffenseInfo允许
	OnUse,	//涌动，在使用动作(Montage)进入对应AnimNotify的时候会激活这个效果，如果条件允许激活的话
	Passive,	//环绕，其实就是被动效果了，非常好理解，用Buff实现的
	Action	//元素激活动作（给予角色一个特有动作）
};

/**
 * TODO : To Be Removed
 * 元素天赋的UI显示内容，当然也包含了学习条件之类的，这些需要以后重构分开的玩意儿
 */
USTRUCT(BlueprintType)
struct FElementalTalentUIInfo
{
	GENERATED_BODY()
public:
	//这个和FElementalTalent将是11对应的
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Id;

	//天赋属于什么元素的，TODO：不是很应该在这里
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EChaElemental Elemental = EChaElemental::Physical;

	//天赋属于什么处罚方式， TODO：不是很应该在这里，但瞅着这里也得有
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EElementalTriggerType ElementalTrigger = EElementalTriggerType::OnHit;

	//天赋属于第几级别的 TODO：不该在这里
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int Level = 1;
	
	//激活这个天赋所需要的EP  TODO：这本不该在这里，待重构
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int UnlockCostEP = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString IconPath;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Name;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString BaseEffect;	//基础效果的文字描述

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<ETerrainType, FString> TerrainEffect;   //地形元素附加的说明

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<EWeatherType, FString> WeatherEffect;	//天气元素附加的说明

	static FElementalTalentUIInfo FromJson(TSharedPtr<FJsonObject> JsonObj);
};

/**
 * TODO: To Be Removed
 * 每一个元素天赋的内容
 */
USTRUCT(BlueprintType)
struct FElementalTalent
{
    GENERATED_BODY();
public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Id;
	
	//效果的激活类型
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EElementalTriggerType ElementalTrigger = EElementalTriggerType::Passive;

	//所属元素科目
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	EChaElemental ElementalType = EChaElemental::Fire;

	//基础效果，实际上都是JsonFunc
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FString> BaseEffect;

	/**
	 * 地形特效，一个元素天赋允许对若干地形有基础效果之外的额外效果
	 * Key是所在地型
	 * Value是特效的JsonFunc，因为傻屌UE不支持Map下有数组，只好策划辛苦点写在一个函数里了
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<ETerrainType, FString> TerrainEffect;

	/**
	 * 天气特效，一个元素天赋允许在特殊天气下有额外效果
	 * Key是所处天气
	 * Value是特效的JsonFunc，因为傻屌UE不支持Map下有数组，只好策划辛苦点写在一个函数里了
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TMap<EWeatherType, FString> WeatherEffect;

	static FElementalTalent FromJson(TSharedPtr<FJsonObject> JsonObj);
};