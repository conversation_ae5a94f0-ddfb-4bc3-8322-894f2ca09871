// Fill out your copyright notice in the Description page of Project Settings.


#include "ButtonUI.h"
#include "Engine/Texture2D.h"

void UButtonUI::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	ButtonIconImage			= Cast<UImage>(GetWidgetFromName("ButtonIcon"));
	ButtonNameTextBlock		= Cast<UTextBlock>(GetWidgetFromName("ButtonName"));
	
}

void UButtonUI::NativeConstruct()
{
	Super::NativeConstruct();
}

void UButtonUI::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
}

void UButtonUI::SetButton(FString ButtonIcon, FString ButtonName)
{
	if(ButtonIconImage)
		ButtonIconImage->SetBrushFromTexture(LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(ButtonIcon)));

	if(ButtonNameTextBlock)
		ButtonNameTextBlock->SetText(FText::FromString(ButtonName));
}
