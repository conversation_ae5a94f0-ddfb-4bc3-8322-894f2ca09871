// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "Dom/JsonObject.h"
#include "CoreMinimal.h"
#include "DefaultConfig.generated.h"

/**
 * 默认设置
 */
USTRUCT(BlueprintType)
struct FDefaultConfig
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadOnly)
	FString TypeId = "TypeA";
	
	UPROPERTY(BlueprintReadOnly)
	FString ClassId = "Warrior";
	
	UPROPERTY(BlueprintReadOnly)
	FString Language = "Chinese";

	UPROPERTY(BlueprintReadOnly)
	TArray<FString> LanguageList;
	
	UPROPERTY(BlueprintReadOnly)
	FString GamepadButtonType = "PlayStation";

	UPROPERTY(BlueprintReadOnly)
	bool IsGourpNameContainAlterId = true;
	
	static FDefaultConfig FromJson(TSharedPtr<FJsonObject> JsonObj);
};
