// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/SceneComponent.h"
#include "CloseUpShotCamera.generated.h"


UCLASS(ClassGroup="Camera", editinlinenew, hidecategories=(Object,LOD,Lighting,TextureStreaming), meta=(DisplayName="[Dialog] Close Up Shot", BlueprintSpawnableComponent))
class THEAWAKENER_FO_API UCloseUpShotCamera : public USceneComponent
{
	GENERATED_BODY()

public:
	//第几号特写摄像机，到时候Dialog脚本中指向的是这个摄像机Index
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int Index = 0;
};
