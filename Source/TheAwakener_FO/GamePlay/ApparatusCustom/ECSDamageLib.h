#pragma once
#include "SubjectHandle.h"
#include "TheAwakener_FO/GamePlay/Characters/Animation/AwAnimInstance.h"
#include "TheAwakener_FO/GamePlay/Characters/Attack/ForceMoveInfo.h"
#include "TheAwakener_FO/GamePlay/Characters/Battle/BattleStructs.h"
#include "Traits/Attacks.h"
#include "ECSDamageLib.generated.h"
UCLASS()
class THEAWAKENER_FO_API UECSDamageLib: public UBlueprintFunctionLibrary
{
	GENERATED_BODY()
public:
	// 功能拆分
	static constexpr EFlagmarkBit DyingFlag = EFlagmarkBit::D;
	static void PlayFX(const FSubjectHandle& Subject, AAwCharacter* Attacker, const FAttackInfo& AttackInfo);
	static void AddHitLog(const FSubjectHandle& Subject,const EFlagmarkBit& Flag,int times,double Duration);
	//清理命中信息
	static void CleanActionHittedECS(EFlagmarkBit HittedEnemyFilter,UWorld* World);
	static void TryHitEnemy(FSubjectHandle& Subject,AAwCharacter* Attacker,EFlagmarkBit Flag,FOffenseInfo OffenseInfo,
		FDamageInfo& DInfo, const FVector& TargetLocation,bool& isHit,bool& isDead);
	static void TryBeepAchievementByHit(AAwCharacter* Character,TArray<FString> Beep);
	static void TryKnockback(FSubjectHandle& Subject,FForceMoveInfo& ForceMoveInfo);
	static void HitFreeze(const FActionChangeInfo& ActionChangeInfo, UAwAnimInstance* AwAnimInstance, const int HitNum);
	static bool HasHitProtection(const FSubjectHandle& Subject,EFlagmarkBit Flag);

	// 包装函数：处理ECS攻击命中后的逻辑
	static int ProcessECSHitLogic(AAwCharacter* Attacker, const FDamageInfo& DInfo, const FOffenseInfo& OffenseInfo, bool IsBullet,
		std::atomic<int32> &SafeHitP1,std::atomic<int32> &SafeHitP2);
	// 包装函数：处理ECS攻击击杀后的逻辑
	static void ProcessECSKillLogic(AAwCharacter* Attacker, FDamageInfo& DInfo,
		std::atomic<int32> &SafeKillP1);
};
UCLASS()
class THEAWAKENER_FO_API UFlagmarkBitPool: public UBlueprintFunctionLibrary
{
	GENERATED_BODY()
public:
	UFUNCTION(BlueprintCallable,Category="ECS|FlagmarkBit",DisplayName="Get")
	static EFlagmarkBit Acquire()
	{
		InitIfNeeded();
		const int32 Count = Keys.Num();

		for (int32 i = 0; i < Count; ++i)
		{
			Index = (Index + 1) % Count;
			EFlagmarkBit Key = Keys[Index];

			if (!Pool[Key])
			{
				Pool[Key] = true;
				return Key;
			}
		}

		// 全部被使用，返回当前轮询位置
		Index = (Index + 1) % Count;
		return Keys[Index];
	}
	UFUNCTION(BlueprintCallable,Category="ECS|FlagmarkBit",DisplayName="Release")
	static void Release(EFlagmarkBit Key)
	{
		InitIfNeeded();
		Pool.FindOrAdd(Key) = false;
	}
	UFUNCTION(BlueprintCallable,Category="ECS|FlagmarkBit",DisplayName="Check")
	static bool Check(EFlagmarkBit Key)
	{
		InitIfNeeded();
		UE_LOG(LogTemp,Display,TEXT("%s = %s"),*StaticEnum<EFlagmarkBit>()->GetNameStringByValue((int64)Key),Pool[Key]?TEXT("True"):TEXT("False"));
		return Pool[Key];
	}
	UFUNCTION(BlueprintCallable,Category="ECS|FlagmarkBit",DisplayName="CheckAll")
	static void CheckAll()
	{
		InitIfNeeded();
		const int32 Count = Keys.Num();

		for (int32 i = 0; i < Count; ++i)
		{
			EFlagmarkBit Key = Keys[i];
			Check(Key);
		}
	}

private:
	static void InitIfNeeded()
	{
		if (bInitialized) return;

		Keys = {
			EFlagmarkBit::H, EFlagmarkBit::I, EFlagmarkBit::J, EFlagmarkBit::K, EFlagmarkBit::L,
			EFlagmarkBit::M, EFlagmarkBit::N, EFlagmarkBit::O, EFlagmarkBit::P, EFlagmarkBit::Q
		};

		for (EFlagmarkBit Key : Keys)
		{
			Pool.Add(Key, false);
		}

		bInitialized = true;
	}
	static inline TMap<EFlagmarkBit, bool> Pool;
	static inline TArray<EFlagmarkBit> Keys;
	static inline int32 Index = -1;
	static inline bool bInitialized = false;
};