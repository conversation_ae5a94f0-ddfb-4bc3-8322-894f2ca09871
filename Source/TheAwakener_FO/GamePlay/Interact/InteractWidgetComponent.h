// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/WidgetComponent.h"
#include "TheAwakener_FO/GameFramework/AwPlayerController.h"
#include "UObject/Interface.h"
#include "BeInteractedBox.h"
#include "InteractWidgetComponent.generated.h"


UINTERFACE(MinimalAPI)
class UInteractWidgetInterface : public UInterface
{
	GENERATED_BODY()
};

class THEAWAKENER_FO_API IInteractWidgetInterface
{
	GENERATED_BODY()

		// Add interface functions to this class. This is the class that will be inherited to implement this interface.
public:
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
		void SetInteractWidgetText(const FString &TextString);
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
		void OnInteractWidgetActive();
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
		void SetInteractWidgetExtraSource( const TMap<FString, FString>& ExParams);
};



UCLASS(Blueprintable, ClassGroup = "Interact", meta = (BlueprintSpawnableComponent))
class THEAWAKENER_FO_API UInteractWidgetComponent : public UWidgetComponent
{
	GENERATED_BODY()

public:

	UInteractWidgetComponent();

	virtual void BeginPlay() override;

	void SetCurWidget(EInteractTargetType InteractType,TSubclassOf<UUserWidget> TargetClass,AAwPlayerController* Controller);

};
