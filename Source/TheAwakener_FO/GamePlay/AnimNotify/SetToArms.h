// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "TheAwakener_FO/GamePlay/Characters/Action/ArmState.h"
#include "UObject/Object.h"
#include "SetToArms.generated.h"

/**
 *  设置为武装状态（开启或者关闭）
 */
UCLASS()
class THEAWAKENER_FO_API USetToArms : public UAnimNotify
{
	GENERATED_BODY()
public:
	//设置为武装状态
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	EArmState ToArms = EArmState::Armed;

	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
