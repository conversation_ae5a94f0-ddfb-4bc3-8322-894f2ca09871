// Fill out your copyright notice in the Description page of Project Settings.


#include "ForceOfProgress.h"

#include "Engine/Texture2D.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void UForceOfProgress::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	GameState				= UGameplayFuncLib::GetAwGameState();
	GameInstance			= UGameplayFuncLib::GetAwGameInstance();

	ForcesOfProgress_Bar		= Cast<UProgressBar>(GetWidgetFromName("ForcesOfProgressBar"));
	Shade_Image					= Cast<UImage>(GetWidgetFromName("Shade"));
	Race_Image					= Cast<UImage>(GetWidgetFromName("PowerIcon"));
	
}

void UForceOfProgress::NativeConstruct()
{
	Super::NativeConstruct();
	
}

void UForceOfProgress::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);

	//UpdateProgress(InDeltaTime);
}

void UForceOfProgress::SetRaceIcon(FString Icon)
{
	UTexture2D* RaceIcon = LoadObject<UTexture2D>(nullptr,
		*UResourceFuncLib::GetAssetPath(UGameplayFuncLib::GetAwDataManager()->GetItemIconById(Icon).Path));
	if(Race_Image && RaceIcon)
	{
		Race_Image->SetBrushFromTexture(RaceIcon);
		Race_Image->SetDesiredSizeOverride(FVector2D(RaceIcon->GetSizeX(),RaceIcon->GetSizeY()));
	}
}

/*
void UForceOfProgress::ChangeProgress(FString MapId)
{
	if(GameInstance)
	{
		for (FAwDungeonSave DungeonSave : GameInstance->RoleInfo.DungeonRecords)
		{
			if(MapId == DungeonSave.DungeonId)
			{
				for (FAwDungeonCampSave DungeonCampSave : DungeonSave.Camps)
				{
					if(DungeonCampSave.CampId == PowerName)
					{
						ForcesValue = DungeonCampSave.CampProgress / 1000.0f;
					}
				}
			}
		}
	}
	else
	{
		GameInstance = UGameplayFuncLib::GetAwGameInstance();
	}
}

void UForceOfProgress::UpdateProgress(float InDeltaTime)
{
	if(!CanChange)
		return;
	
	ForcesOfProgress_Bar->SetPercent(FMath::FInterpTo(
					ForcesOfProgress_Bar->Percent,
					ForcesValue,InDeltaTime,ChangeForcesValueSpeed));
	
	Shade_Image->SetRenderTranslation(FMath::Vector2DInterpTo(
		Shade_Image->RenderTransform.Translation,
		FVector2D(ForcesValue * ProgressSizeX,0.0f),InDeltaTime,ChangeForcesValueSpeed));
}
*/
