// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "Dom/JsonObject.h"
#include "CoreMinimal.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "AnimPicker.generated.h"

/**
 * 检测当前角色状态，调整播放动画的数据
 */
USTRUCT(BlueprintType)
struct FAnimPicker
{
	GENERATED_BODY()
public:
	//检查用的Function，每帧都会调，如果这个是空，那么就是不做检查的
	//这些函数都在DesignerScript下
	//(AAwCharacter*, FActionParam, DesignerParams)=>int，输入的是角色和当前的参数，返回的是AnimPath的index
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString TickChecker;

	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool CheckOnTick = true;
	
	//对应每个蒙太奇的文件路径，从ArtResource这层开始的，必须至少有1个，如果没有Func，那么执行的就是这个
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> AnimPath;
	
	//是否是每一帧都要重新算一次TickChecker的，否代表只运行一次
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool Period = true;

	//记录一个TickChecker所选择的结果
	int LastCheckerRes = 0;

	static FAnimPicker Create(FString FuncKey, TArray<FString>Paths)
	{
		FAnimPicker AnimPicker = FAnimPicker();
		AnimPicker.TickChecker = FuncKey;//UCallFuncLib::StringToUFuncion(FuncKey);
		AnimPicker.AnimPath = Paths;
		return AnimPicker;
	}

	static FAnimPicker FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FAnimPicker Model;
		Model.TickChecker = UDataFuncLib::AwGetStringField(JsonObj, "StateFunc");
		Model.CheckOnTick = UDataFuncLib::AwGetBoolField(JsonObj, "CheckOnTick", false);
		Model.Period = UDataFuncLib::AwGetBoolField(JsonObj, "Period", true);
		
		TArray<TSharedPtr<FJsonValue, ESPMode::ThreadSafe>>Obj = UDataFuncLib::AwGetArrayField(JsonObj, "Anim");
		for (int i = 0; i < Obj.Num(); i++)
			Model.AnimPath.Add(Obj[i]->AsString());
		
		// if (JsonObj->HasField("CanStopSprint"))
		// {
		// 	TArray<TSharedPtr<FJsonValue, ESPMode::ThreadSafe>> BoolObjs = UDataFuncLib::AwGetArrayField(JsonObj, "CanStopSprint");
		// 	for (int i = 0; i < BoolObjs.Num(); i++)
		// 		Model.CanStopSprint.Add(BoolObjs[i]->AsBool());
		// }
		// else
		// {
		// 	for (int i = 0; i < Model.AnimPath.Num(); i++)
		// 		Model.CanStopSprint.Add(true);
		// }
		
		return Model;
	}

	bool operator ==(const FAnimPicker& Other) const
	{
		return this->AnimPath == Other.AnimPath && this->TickChecker == Other.TickChecker && this->Period == Other.Period;		
	}
};
