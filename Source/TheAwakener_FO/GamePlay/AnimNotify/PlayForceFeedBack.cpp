

#include "PlayForceFeedBack.h"

#include "Components/SkeletalMeshComponent.h"
#include "Kismet/GameplayStatics.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void UPlayForceFeedBack::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
                                const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);

	UPlayForceFeedBack::Play(AwForceFeedBackInfo, MeshComp);
}

void UPlayForceFeedBack::Play(const FAwForceFeedBackInfo& AwForceFeedBackInfo, USkeletalMeshComponent* MeshComp)
{
	if (!URogueGameSetting::GetRogueGameSettings()->GetGamepadFeedBack())
		return;
	auto pc = UGameplayFuncLib::GetPlayerControllerByComp(MeshComp,false);
	if (!pc)
	{
		return;
	}
	
	if (AwForceFeedBackInfo.ForceFeedbackEffect)
	{
		if (AwForceFeedBackInfo.IsPlayOnGamepad)
		{
			if (pc)
				pc->ClientPlayForceFeedback(AwForceFeedBackInfo.ForceFeedbackEffect);
		}
		else if (AwForceFeedBackInfo.IsAttach)
		{
			if (MeshComp != nullptr)
			{
				UGameplayStatics::SpawnForceFeedbackAttached(
					AwForceFeedBackInfo.ForceFeedbackEffect,
					MeshComp,
					AwForceFeedBackInfo.SocketName,
					AwForceFeedBackInfo.PosOffset,
					FRotator::ZeroRotator,
					EAttachLocation::KeepRelativeOffset,
					false,
					false,
					AwForceFeedBackInfo.IntensityMultiplier,
					0,
					AwForceFeedBackInfo.ForceFeedbackAttenuation,
					true);
			}
		}
		else
		{
			UGameplayStatics::SpawnForceFeedbackAtLocation(
				UGameplayFuncLib::GetAwGameMode(),
				AwForceFeedBackInfo.ForceFeedbackEffect,
				AwForceFeedBackInfo.PosOffset,
				FRotator::ZeroRotator,
				false,
				AwForceFeedBackInfo.IntensityMultiplier,
				0,
				AwForceFeedBackInfo.ForceFeedbackAttenuation,
				true);
		}
	}
}
