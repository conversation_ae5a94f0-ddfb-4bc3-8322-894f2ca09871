// Fill out your copyright notice in the Description page of Project Settings.

#pragma once
#include "Dom/JsonObject.h"
#include "CoreMinimal.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "DamageValue.generated.h"

/**
 * 不同类型的伤害与值
 * 其实防御相关的也就这样了，为了避免幺蛾子，还是先留下结构
 */
USTRUCT(BlueprintType)
struct FDamageValue
{
	GENERATED_BODY()
public:
	/**
	 * 物理伤害
	 * 在动作中代表百分比动作值，1=100%的意思。
	 * 在非动作中（直接产生OffenseInfo等，比如Aoe Buff等），1=1点伤害力
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float Physical = 0;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float Element = 0;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float Break = 1;

	
	FDamageValue(float PhysicalValue = 0, float FireValue = 0, float BreakValue = 1):
		Physical(PhysicalValue), Break(BreakValue){};

	FDamageValue operator+(const FDamageValue& Other ) const
	{
		return FDamageValue(
			this->Physical + Other.Physical,0,this->Break + Other.Break
		);
	}

	FDamageValue operator*(const FDamageValue& Other ) const
	{
		return FDamageValue(
			this->Physical * Other.Physical,0,this->Break * Other.Break
		);
	}
	FDamageValue operator*(const float& Times ) const
	{
		return FDamageValue(
			this->Physical * Times,0,this->Break * Times
		);
	}

	FDamageValue operator/(const FDamageValue& Other ) const
	{
		return FDamageValue(
			 FMath::IsNearlyZero(Other.Physical) ? this->Physical : (this->Physical / Other.Physical),0,
			 FMath::IsNearlyZero(Other.Break) ? this->Break : (this->Break / Other.Break)
		);
	}

	bool operator == (const FDamageValue& Other) const
	{
		bool Res = this->Physical == Other.Physical && this->Element == Other.Element && this->Break == Other.Break;
		return Res;
	}

	void FromJson(const TSharedPtr<FJsonObject>& JsonObj)
	{
		this->Physical = UDataFuncLib::AwGetNumberField(JsonObj, "Physical", 0.0f);
		this->Element = UDataFuncLib::AwGetNumberField(JsonObj, "Element", 0.0f);
		this->Break = UDataFuncLib::AwGetNumberField(JsonObj, "Break", 1.0f);
	}

	/**
	 * 总的伤害值
	 */
	int TotalDamage() const
	{
		int Res =FMath::RoundToInt(this->Physical + this->Element);
		return Res;
	}

	float TotalBreak() const
	{
		return this->Break;
	}

	void Add(FString Key, float Value)
	{
		this->Physical += Value;
		this->Element += Value;
		this->Break += Value;
	}

	void SetDamageZero()
	{
		this->Physical = 0;
		this->Element = 0;
		this->Break = 0;
	}

	void SetAllTo(float Value)
	{
		this->Physical = FMath::Min(1.00f, Value);
		this->Break = FMath::Min(1.00f, Value);
	}
};
