// Fill out your copyright notice in the Description page of Project Settings.


#include "ChangeActionState.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"


void UChangeActionState::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);

	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	Me->ChangeCharacterState(TargetState);
	if (TargetState==ECharacterActionState::Flying)
	{
		Me->GetCharacterMovement()->SetMovementMode(MOVE_Flying);
	}
	else if (TargetState == ECharacterActionState::Ground)
	{
		Me->GetCharacterMovement()->SetMovementMode(MOVE_Walking);
	}
}
