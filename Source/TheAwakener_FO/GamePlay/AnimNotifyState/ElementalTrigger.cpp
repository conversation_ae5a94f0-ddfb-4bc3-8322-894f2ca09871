// Fill out your copyright notice in the Description page of Project Settings.


#include "ElementalTrigger.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UElementalTrigger::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyBegin(MeshComp, Animation, TotalDuration, EventReference);
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	Me->AddElementalTriggerTags(this->ElementalTriggerTags);
}

void UElementalTrigger::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyEnd(MeshComp, Animation, EventReference);
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	Me->RemoveElementalTriggerTags(this->ElementalTriggerTags);
}

