// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "UObject/Object.h"
#include "DebugFuncLib.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UDebugFuncLib : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()
	
public:
	UFUNCTION(BlueprintCallable)
	static void KillMyself(int playerIndex);

	UFUNCTION(BlueprintCallable)
	static void RevivedMyself(int playerIndex);

	UFUNCTION(BlueprintCallable)
	static void FullyRestore(int playerIndex);

	UFUNCTION(BlueprintCallable)
	static void KillAllMobs();

	UFUNCTION(BlueprintCallable)
	static void KillAllEnemy();
};
