// Fill out your copyright notice in the Description page of Project Settings.


#include "ElementalIcon.h"

#include "Engine/Texture2D.h"
#include "TheAwakener_FO/FunctionLibrary/ResourceFuncLib.h"

void UElementalIcon::NativeOnInitialized()
{
	Super::NativeOnInitialized();
	this->TalentIcon = Cast<UImage>(GetWidgetFromName(TEXT("Icon")));
	this->LockIcon = Cast<UImage>(GetWidgetFromName(TEXT("LockSign")));
	this->SelectMask = Cast<UImage>(GetWidgetFromName(TEXT("SelectSign")));
	this->EPText = Cast<UTextBlock>(GetWidgetFromName(TEXT("EPRequire")));
	this->ThisButton = Cast<UButton>(GetWidgetFromName(TEXT("BackButton")));
	this->ThisButton->OnClicked.AddDynamic(this, &UElementalIcon::ClickOnMe);
	this->OccurTypeIcon = Cast<UImage>(GetWidgetFromName(TEXT("TypeIcon")));
}

void UElementalIcon::NativeOnItemSelectionChanged(bool bIsSelected)
{
	if (this->SelectMask)
	{
		this->SelectMask->SetVisibility(bIsSelected ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Hidden);
	}
}

void UElementalIcon::NativeOnListItemObjectSet(UObject* ListItemObject)
{
	const UElementalIcon* Logic = Cast<UElementalIcon>(ListItemObject);
	this->Set(Logic->HintPanel, Logic->UIInfo, Logic->Learnt);
	this->Draw();
}

void UElementalIcon::Draw()
{
	if (this->TalentIcon)
	{
		UTexture2D* IconTexture = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(this->UIInfo.IconPath));
		if (IconTexture)
		{
			TalentIcon->SetVisibility(ESlateVisibility::Visible);
			TalentIcon->SetBrushFromTexture(IconTexture);
		}else
		{
			TalentIcon->SetVisibility(ESlateVisibility::Hidden);
		}
	}
	if (this->LockIcon)
	{
		this->LockIcon->SetVisibility(Learnt ? ESlateVisibility::Hidden : ESlateVisibility::HitTestInvisible);
	}
	if (this->SelectMask)
	{
		this->SelectMask->SetVisibility(ESlateVisibility::Hidden);	//TODO
	}
	if (this->EPText)
	{
		FText ShowText = FText::FromString(FString("--"));
		if (Learnt == false) ShowText = FText::FromString(FString::FromInt(UIInfo.UnlockCostEP));
		this->EPText->SetText(ShowText);
	}
	if (OccurTypeIcon)
	{
		FString APath = FString();
		switch (UIInfo.ElementalTrigger)
		{
		case EElementalTriggerType::Action: APath = "ArtResource/UI/UI/Elemental/Icon/occur_onaction"; break;
		case EElementalTriggerType::Passive: APath = "ArtResource/UI/UI/Elemental/Icon/occur_passive"; break;
		case EElementalTriggerType::OnHit: APath = "ArtResource/UI/UI/Elemental/Icon/occur_onhit"; break;
		case EElementalTriggerType::OnUse: APath = "ArtResource/UI/UI/Elemental/Icon/occur_onuse"; break;
		}
		if (APath.IsEmpty() == false)
		{
			UTexture2D* IconTexture = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(APath));
			if (IconTexture)
			{
				OccurTypeIcon->SetVisibility(ESlateVisibility::Visible);
				OccurTypeIcon->SetBrushFromTexture(IconTexture);
			}else
			{
				OccurTypeIcon->SetVisibility(ESlateVisibility::Hidden);
			}
		}
	}
}

void UElementalIcon::Set(UElementalTalentHint* Hint, FElementalTalentUIInfo TalentUIInfo, bool AsLearnt)
{
	this->UIInfo = TalentUIInfo;
	this->Learnt = AsLearnt;
	this->HintPanel = Hint;
	Draw();
}

void UElementalIcon::ClickOnMe()
{
	if (this->HintPanel)
	{
		HintPanel->Set(this->UIInfo, this->Learnt);
	}
}
