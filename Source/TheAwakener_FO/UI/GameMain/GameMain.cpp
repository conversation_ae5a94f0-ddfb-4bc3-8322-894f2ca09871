// Fill out your copyright notice in the Description page of Project Settings.


#include "GameMain.h"

#include "Engine/Texture2D.h"
#include "Animation/UMGSequencePlayer.h"
#include "Blueprint/WidgetLayoutLibrary.h"
#include "Kismet/KismetMathLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/ResourceFuncLib.h"
#include "TheAwakener_FO/GamePlay/Trading/MerchantShop.h"

void UGameMain::NativeOnInitialized()
{
	Super::NativeOnInitialized();
	ShowingCharacter = UGameplayFuncLib::GetAwGameState() ? UGameplayFuncLib::GetAwGameState()->GetMyCharacter() : nullptr;
	
	PlayerNameText = Cast<UTextBlock>(GetWidgetFromName(TEXT("PlayerName")));
	HpBar = Cast<UProgressBar>(GetWidgetFromName(TEXT("HPBarView")));
	TeamList = Cast<UListView>(GetWidgetFromName(TEXT("OtherPlayerList")));
	AreaNameText = Cast<UTextBlock>(GetWidgetFromName(TEXT("AreaName")));
	UsingItemIcon = Cast<UImage>(GetWidgetFromName(TEXT("ItemIcon")));
	ItemCount = Cast<UTextBlock>(GetWidgetFromName(TEXT("Text_ItemCount")));
	ClassIconComp = Cast<UImage>(GetWidgetFromName(TEXT("ClassIcon")));
	GotItemArea = Cast<UCanvasPanel>(GetWidgetFromName(TEXT("GetThings")));
	PlayerStateBarUI = Cast<UCharacterStateBarUI>(GetWidgetFromName(TEXT("WBP_PlayerStateBar")));
	MyPlayerController = UGameplayFuncLib::GetWorkingAwPlayerController();
	UWidget* HC = GetWidgetFromName(TEXT("HitsCombo"));
	UKismetSystemLibrary::PrintString(this, HC ? "Has":"No");
	HitComboWidget = Cast<UHitComboWidget>(GetWidgetFromName(TEXT("HitsCombo")));
	/*MainTask = Cast<UTaskUI>(GetWidgetFromName(TEXT("WBP_MainTask")));
	BranchTask = Cast<UTaskUI>(GetWidgetFromName(TEXT("WBP_BranchTask")));*/

	MainTaskVerticalBox = Cast<UVerticalBox>(GetWidgetFromName(TEXT("MainTask_VerticalBox")));
	BrabchTaskVerticalBox = Cast<UVerticalBox>(GetWidgetFromName(TEXT("BrabchTask_VerticalBox")));

	//凑效果
	ChangeClassOrSkillTask = Cast<UTaskUI>(GetWidgetFromName(TEXT("WBP_ChangeClassOrSkill")));
	BlackSmithTask = Cast<UTaskUI>(GetWidgetFromName(TEXT("WBP_BlackSmithTask")));

	ChangeClassOrSkillTask->TaskComplateDelegate.AddDynamic(this,&UGameMain::TempTaskMove);
	
	MyCurAnimTime = 0.0f;																		//当前动画播放时间
	FinishEvent.BindDynamic(this,&UGameMain::AnimIsComplete);									//委托事件绑定执行的函数
	CanShowUI = false;
	
	SetAreaNameText();
}

void UGameMain::NativeConstruct()
{
	Super::NativeConstruct();

	if(ChangeClassOrSkillTask->TaskIsComplate)
	{
		BlackSmithTask->SetRenderTranslation(FVector2D(BlackSmithTask->GetRenderTransform().Translation.X,0));
	}
	/*MainTask->TaskCreateDelegate.AddDynamic(this,&UGameMain::PlayMainTaskAnim);
	BranchTask->TaskCreateDelegate.AddDynamic(this,&UGameMain::PlayBranchTaskAnim);*/
}

void UGameMain::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);
	if (!ShowingCharacter) ShowingCharacter = UGameplayFuncLib::GetUiManager()->AwGameState ? UGameplayFuncLib::GetUiManager()->AwGameState->GetMyCharacter() : nullptr;
	if (!ShowingCharacter) return;
	if (HpBar) HpBar->SetPercent(ShowingCharacter->HealthPercentage());

	if (ShowingCharacter->IsActionOccur("UseItem", EAwInputState::Press, true))
	{
		StartUseSelectedItem();
	}else if (ShowingCharacter->IsActionOccur("ItemRight", EAwInputState::Press, true))
	{
		SelectNextItem();
	}else if (ShowingCharacter->IsActionOccur("ItemLeft", EAwInputState::Press, true))
	{
		SelectPrevItem();
	}
	
	if (ClassIconComp)
	{
		const EClassWeaponType Weapon = ShowingCharacter->CharacterObj.WeaponSet.WeaponType;
		FString ClassIconShould;
		switch (Weapon)
		{
		case EClassWeaponType::PoleArm: ClassIconShould = "ArtResource/UI/Icon/Class/class_icon_Spearman_"; break;
		case EClassWeaponType::TwinSword: ClassIconShould = "ArtResource/UI/Icon/Class/class_icon_BladeDancer"; break;
			default:ClassIconShould = "ArtResource/UI/Icon/Class/class_icon_Warrior"; break;
		}
		if (MyClassIcon != ClassIconShould)
		{
			MyClassIcon = ClassIconShould;
			UTexture2D* IconTexture = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath( ClassIconShould));
			if (IconTexture)
			{
				ClassIconComp->SetBrushFromTexture(IconTexture);
			}
		}
	}

	/*if (SecToNextNewThing > 0)
	{
		SecToNextNewThing -= InDeltaTime;
	}else
	{
		if (GoingToShowNewThings.Num() > 0)
		{
			UNewThing* NThing =  UAwGameInstance::Instance->UIManager->LoadUserWidget<UNewThing>("Core/UI/GameMain/WBP_NewThing");
			if (NThing)
			{
				NThing->Set(GoingToShowNewThings[0]);
				if (GotItemArea)
					GotItemArea->AddChild(NThing);
				NewThings.Add(NThing);
				//GoingToShowNewThings.RemoveAt(0);
				SecToNextNewThing = 1.6f;			//没这么多时间出现一个获得新物品的提示
			}
		}
	}*/

	/*TArray<UNewThing*> ToRemove;
	for (UNewThing* NThing : NewThings)
	{
		NThing->TimeElapsed += InDeltaTime;
		constexpr  float InToScreen = 0.1f;
		constexpr float StayTime = 1.5f;
		constexpr float GoOutTime = 0.3f;
		constexpr float XTotalMove = 150.00f;
		constexpr float YTotalMove = -100.00f;
		if (NThing->TimeElapsed < InToScreen)
		{
			UCanvasPanelSlot* CPS = Cast<UCanvasPanelSlot>(NThing->Slot);
			if (CPS) CPS->SetPosition(FVector2D(-XTotalMove * NThing->TimeElapsed / InToScreen, YTotalMove));
			//NThing->SetPositionInViewport(FVector2D(-XTotalMove * NThing->TimeElapsed / InToScreen));
		}else if (NThing->TimeElapsed < (InToScreen + StayTime))
		{
			UCanvasPanelSlot* CPS = Cast<UCanvasPanelSlot>(NThing->Slot);
			if (CPS) CPS->SetPosition(FVector2D(-XTotalMove, YTotalMove));
			//const float TPass = NThing->TimeElapsed - InToScreen;
			//Stay，不用做任何事情
		}else if (NThing->TimeElapsed <  (InToScreen + StayTime + GoOutTime))
		{
			const float TPass = NThing->TimeElapsed - (InToScreen + StayTime);
			UCanvasPanelSlot* CPS = Cast<UCanvasPanelSlot>(NThing->Slot);
			if(IsMulti)
			{
				if (CPS) CPS->SetPosition(FVector2D(-XTotalMove, CPS->GetSize().Y - CPS->GetSize().Y * TPass / GoOutTime));
			}
			
			//if (CPS) CPS->SetPosition(FVector2D(-XTotalMove, YTotalMove - YTotalMove * TPass / GoOutTime));
			//NThing->SetPositionInViewport(FVector2D(XTotalMove, YTotalMove - YTotalMove * TPass / GoOutTime));
			NThing->SetRenderOpacity(1 - TPass / GoOutTime);
		}else
		{
			ToRemove.Add(NThing);
			NThing->RemoveFromParent();
		}
	}
	for (UNewThing* Remove : ToRemove)
	{
		NewThings.Remove(Remove);
	}*/ 
	
	if (UAwGameInstance::Instance)
	{
		//如果玩家选中的东西还在就保留，否则自动选一个或者置空
		const FItemObj* SelItem = UAwGameInstance::Instance->RoleInfo.QuickSlotFocusItem();
		const FString ItemId = SelItem ? SelItem->Model.Id : "";
		const FThingUIInfo UIInfo = UGameplayFuncLib::GetDataManager()->GetBaseThingUIInfo(EThingType::Item, ItemId);

		if (ItemId.IsEmpty() || UIInfo.Icon.IsEmpty())
		{
			/*const FString ToShow = "ArtResource/UI/Icon/Item/potion_empty";	//TODO 写死
			if (ToShow != ShowingIcon)
			{
				ShowingIcon = ToShow;
				UTexture2D* IconTexture = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath( ToShow));
				if (IconTexture)
				{
					UsingItemIcon->SetBrushFromTexture(IconTexture);
					UsingItemIcon->SetOpacity(0.3f);
				}
			}*/
			UsingItemIcon->SetRenderOpacity(0);
			if (ItemCount)
			{
				ItemCount->SetText(FText::FromString(FString("--")));
			}
		}else
		{
			if (UIInfo.Icon != ShowingIcon)
			{
				ShowingIcon = UIInfo.Icon;
				UTexture2D* IconTexture = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath( UIInfo.Icon));
				if (IconTexture)
				{
					UsingItemIcon->SetBrushFromTexture(IconTexture);
				}
			}
			if (ItemCount)
			{
				ItemCount->SetText(FText::FromString(FString::FromInt(UAwGameInstance::Instance->RoleInfo.GetItemObjCount(ItemId))));
			}
			UsingItemIcon->SetRenderOpacity(1);
		}
		
	}
	
}

void UGameMain::ShowHitCombo(int Combo, float EndInSec) const
{
	if (!HitComboWidget) return;
	HitComboWidget->ShowNumber(Combo, EndInSec);
}

void UGameMain::SetAreaNameText()
{
	AreaNameText->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey(UGameplayFuncLib::GetAwGameInstance()->CurMapInfo.MapId)));
}

void UGameMain::PlayMainTaskAnim()
{
	PlayWidgetAnim("MainTaskShow",0.0f,1,EUMGSequencePlayMode::Forward,1,false);
}

void UGameMain::PlayBranchTaskAnim()
{
	PlayWidgetAnim("BranchTaskShow",0.0f,1,EUMGSequencePlayMode::Forward,1,false);
}

void UGameMain::TempTaskMove()
{
	//ChangeClassOrSkillTask->TaskComplateDelegate.RemoveDynamic(this,&UGameMain::TempTaskMove);

	FTimerDynamicDelegate TimerDynamicDelegate;
	TimerDynamicDelegate.BindDynamic(this,&UGameMain::UpdateTaskPos);
	TimerHandle = UKismetSystemLibrary::K2_SetTimerDelegate(TimerDynamicDelegate,0.002,true);
}

void UGameMain::UpdateTaskPos()
{
	if(UKismetMathLibrary::NearlyEqual_FloatFloat(ChangeClassOrSkillTask->BasePanel->GetRenderOpacity(),0,0.0001f))
	{
		if(UKismetMathLibrary::NearlyEqual_FloatFloat(BlackSmithTask->GetRenderTransform().Translation.Y,-46.0f,0.0001f))
		{
			UKismetSystemLibrary::K2_ClearTimerHandle(this,TimerHandle);
		}
		else
		{
			BlackSmithTask->SetRenderTranslation(FVector2D(BlackSmithTask->GetRenderTransform().Translation.X,
				UKismetMathLibrary::Lerp(BlackSmithTask->GetRenderTransform().Translation.Y,-46.0f,
				UKismetSystemLibrary::K2_GetTimerElapsedTimeHandle(this,TimerHandle))));
		}
	}
}

void UGameMain::PlayPlayerStateBarManaNUllFX()
{
	if(IsValid(PlayerStateBarUI))
	{
		PlayerStateBarUI->PlayManaNullFX();
	}
}

/*void UGameMain::NewThingHint(FThingObj Thing)
{
	this->GoingToShowNewThings.Add(Thing);
	UNewThing* NThing =  UAwGameInstance::Instance->UIManager->LoadUserWidget<UNewThing>("Core/UI/GameMain/WBP_NewThing");
	if(!IsValid(NThing)) return;
	NThing->Set(Thing);
	if (GotItemArea)
		GotItemArea->AddChild(NThing);
	NewThings.Add(NThing);
	if (GoingToShowNewThings.Num() > 1)
	{
		IsMulti = true;
		
		//GoingToShowNewThings.RemoveAt(0);
		//SecToNextNewThing = 1.6f;			//没这么多时间出现一个获得新物品的提示
	}
	else
	{
		IsMulti = false;
	}
	/*UNewThing* NThing = UAwGameInstance::Instance->UIManager->LoadUserWidget<UNewThing>("Core/UI/GameMain/WBP_NewThing");
	if (NThing)
	{
		NThing->Set(Thing);
		if (GotItemArea)
		{
			UWidgetLayoutLibrary::SlotAsCanvasSlot(NThing)->SetPosition(FVector2D(200.0f,0));
			GotItemArea->AddChild(NThing);
		}

		if(GotItemArea->GetChildrenCount() > 1)
		{
			for (UObject* Temp : GotItemArea->GetAllChildren())
			{
				if(!IsValid(Temp)) continue;
				UNewThing* TempThing = Cast<UNewThing>(Temp);
				if(TempThing)
				{
					UWidgetLayoutLibrary::SlotAsCanvasSlot(TempThing)->GetPosition()
					NewThingAndTargetPosition.Add(TempThing,)
				}
			}
		}
		//NewThings.Add(NThing);
	}#1#
}*/

/*UWidgetAnimation* UGameMain::GetNameWidgetAnimation(const FString& InWidgetAnimName)
{
	//获取Widget蓝图生成类
	if (UWidgetBlueprintGeneratedClass* WidgetBlueprintGenerated = Cast<UWidgetBlueprintGeneratedClass>(GetClass()))
	{
		//获取类中的WidgetAnim
		TArray<UWidgetAnimation*> TArrayAnimations = WidgetBlueprintGenerated->Animations;
 
		//通过传入的动画名，找到对应的WidgetAnim
		UWidgetAnimation** MyTempAnimation = TArrayAnimations.FindByPredicate(
				[&](const UWidgetAnimation* OurAnimation)
				{
						return OurAnimation->GetFName().ToString() == (InWidgetAnimName + FString("_INST"));
				});

		if (!MyTempAnimation) return nullptr;
		return *MyTempAnimation;
	}
 
	return nullptr;
}

void UGameMain::PlayWidgetAnim(const FString& InWidgetAnimName, float StartAtTime, int NumLoopsToPlay,
	EUMGSequencePlayMode::Type PlayModeType, float PlayBackSpeed, bool RestoreState)
{

	//判断UI动画
	if (UWidgetAnimation* TempAnimation = this->GetNameWidgetAnimation(InWidgetAnimName))
	{
		BindToAnimationFinished(TempAnimation, FinishEvent);
		//播放此WidgetAnim
		PlayAnimation(TempAnimation,StartAtTime,NumLoopsToPlay,PlayModeType,PlayBackSpeed,RestoreState);
		MyCurAnimation = TempAnimation;
		UE_LOG(LogTemp,Log,TEXT("%s"),*MyCurAnimation->GetName());
	}
}*/

void UGameMain::ShowMainUI(const FString& InWidgetAnimName)
{
	if(CanShowUI)
	{
		if(IsAnimationPlaying(MyCurAnimation))
		{
			UnbindFromAnimationFinished(MyCurAnimation,FinishEvent);				//进入先判断是否有UI动画正在播放，有就将正播放的动画绑定时事件解绑再停止动画
			PauseAnimation(MyCurAnimation);
		}
	
		MyCurAnimTime = this->GetAnimationCurrentTime(MyCurAnimation);
		float ReverseAnimStartTime = 0;
	
		if(MyCurAnimTime == 0.0f)
		{
			ReverseAnimStartTime = MyCurAnimTime;									//当之前动画播放完成时 就可以直接使用当前播放UI动画时间位置播放接下来的UI动画
		}																			//当打断之前的UI动画时 是倒放UI动画所以需要 获取UI动画结束时间 - 当前UI动画播放时间 的位置倒放UI动画	
		else
		{
			ReverseAnimStartTime = MyCurAnimation->GetEndTime() - MyCurAnimTime;
		}
		
		
		PlayWidgetAnim(InWidgetAnimName,ReverseAnimStartTime,1,EUMGSequencePlayMode::Reverse,1.0f,false);
		CanShowUI = false;
	}
	
}

void UGameMain::HiddeMainUI(const FString& InWidgetAnimName)
{
	if(!CanShowUI)
	{
		MyCurAnimTime = this->GetAnimationCurrentTime(MyCurAnimation);
		PlayWidgetAnim(InWidgetAnimName,MyCurAnimTime,1,EUMGSequencePlayMode::Forward,1.0f,false);		//正向播放UI动画就直接根据当前的UI动画时间位置播放
		CanShowUI = true;
	}
}


void UGameMain::Rest()
{
	MyCurAnimTime = 0.0f;
}

void UGameMain::AnimIsComplete()
{
 	UnbindFromAnimationFinished(MyCurAnimation, FinishEvent);

	
	if(CanShowUI)
	{
		if(!IsValid(MyPlayerController))
			MyPlayerController = UGameplayFuncLib::GetWorkingAwPlayerController();
		if(MyPlayerController && MyPlayerController->GameControlState == EGameControlState::Shopping)
		{
			CurShopItem->ShowShopPriceUI();
			//CurShopItem->SetItemComponentAttachInCurSelect();
			//CurShopItem->SetShopItemUI();
			//CurShopItem->ShowShopItemUI(true);
		}
	}
	
}


void UGameMain::StartUseSelectedItem(EItemUseMethod UseMethod) const
{
	const FItemObj* ToUse = UAwGameInstance::Instance->RoleInfo.QuickSlotFocusItem();
	if (!ToUse || !ShowingCharacter || ShowingCharacter->Dead()) return;	//TODO 如果死了也要能用道具，就不该判断Dead()

	FItemUseMethod UseWay;
	switch (UseMethod)
	{
	case EItemUseMethod::Use: UseWay = ToUse->Model.OnUse; break;
	case EItemUseMethod::Throw: UseWay = ToUse->Model.OnThrow; break;
	case EItemUseMethod::Enchant: UseWay = ToUse->Model.OnEnchant; break;
	}
	
	UAwGameInstance::Instance->RoleInfo.SetQuickSlotFocusItemToUsingItem();
	ShowingCharacter->PreorderActionWithCancelCheck(UseWay.UseActionId);
}

void UGameMain::SelectPrevItem() const
{
	UAwGameInstance::Instance->RoleInfo.PrevQuickSlot();
	UGameplayFuncLib::PlayUIAudio("SwitchKey_LeftRight");
}

void UGameMain::SelectNextItem() const
{
	UAwGameInstance::Instance->RoleInfo.NextQuickSlot();
	UGameplayFuncLib::PlayUIAudio("SwitchKey_LeftRight");
}


