// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "Dom/JsonObject.h"
#include "CoreMinimal.h"
#include "PathNodeQueue.generated.h"

/**
 * 配置的地图寻路点数据，用于产生连续移动寻路点数据
 */
USTRUCT()
struct FPathNodeQueueInfo
{
	GENERATED_BODY()
public:
	//这个寻路队列的Id
	UPROPERTY()
	FString Id;
	
	//点阵名称，需要按顺序排列
	UPROPERTY()
	TArray<FString> NodeIds;

	//这个PathNode是否是循环的
	UPROPERTY()
	bool Loop = true;

	FPathNodeQueueInfo(){}
	FPathNodeQueueInfo(FString QueueId, TArray<FString> IdOfNodes, bool IsLoop):
		Id(QueueId), NodeIds(IdOfNodes), Loop(IsLoop){}

	static FPathNodeQueueInfo FromJson(TSharedPtr<FJsonObject> JsonObj);
};


/**
 * 连续移动的路径点信息
 */
USTRUCT(BlueprintType)
struct FPathNodeQueue
{
	GENERATED_BODY()
public:
	//这个寻路队列的Id
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString Id;
	
	//路径点阵<点id, 点坐标>
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	TArray<FVector> Nodes;

	//是否循环，即最后一个完成的时候会回到第一个
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	bool Loop = true;

	//当前所处的是第几个路径点下标
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	int NodeIndex = -1;

	FPathNodeQueue(){};
	FPathNodeQueue(TArray<FVector> PathNodes, bool IsLoop):
		Nodes(PathNodes), Loop(IsLoop), NodeIndex(PathNodes.Num() > 0 ? 0 : -1){};

	/**
	 * 获取最近的Node的Index
	 * @param CurLocation 当然是距离这个点最近的
	 * @return  返回那个点的下标，而不是NodeIndex，需要的话请手动设置
	 */
	int GetNearestNodeIndex(FVector CurLocation);

	/**
	 * 跳转到下一个Node，并且返回下标
	 */
	int NextNode();

	/**
	 * 设置到Node，并且返回设置后的下标
	 * @param ToIndex 目标下标
	 * @return 最终的下标，未必==目标下标，因为可能脱离了这个数组的，所以返回值是[-1, Nodes.Num()-1]的，如果点不存在就会-1，或者超过了长度
	 */
	int SetNodeToIndex(int ToIndex);
};
