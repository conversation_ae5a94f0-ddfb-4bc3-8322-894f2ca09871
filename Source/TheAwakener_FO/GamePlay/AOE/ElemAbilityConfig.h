// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd

#pragma once

#include "CoreMinimal.h"
#include "Dom/JsonObject.h"
#include "ElemAbilityConfig.generated.h"

/**
 * 
 */
USTRUCT(BlueprintType)
struct THEAWAKENER_FO_API FElemAbilityConfig
{
	GENERATED_BODY()
	public:
    // 数组会自动对应 ini 文件里的多个同名键
    UPROPERTY(BlueprintReadOnly, EditAnywhere, Category="Elemental Ability Config")
    TArray<float> DamageMultipliers;
	static FElemAbilityConfig FromJson(TSharedPtr<FJsonObject> JsonObj);
};
