// Fill out your copyright notice in the Description page of Project Settings.


#include "WeaponSlot.h"

#include "Engine/Texture2D.h"
#include "Kismet/KismetSystemLibrary.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/ResourceFuncLib.h"
#include "TheAwakener_FO/UI/Menu/CharacterUI.h"

FReply UWeaponSlot::NativeOnFocusReceived(const FGeometry& InGeometry, const FFocusEvent& InFocusEvent)
{
	FReply Res = Super::NativeOnFocusReceived(InGeometry, InFocusEvent);

	UKismetSystemLibrary::PrintString(this, FString("On Me"));

	return Res;
}

void UWeaponSlot::NativeOnFocusLost(const FFocusEvent& InFocusEvent)
{
	Super::NativeOnFocusLost(InFocusEvent);

	UKismetSystemLibrary::PrintString(this, FString("Leave Me Alone"));
}

void UWeaponSlot::NativeOnInitialized()
{
	Super::NativeOnInitialized();
	WeaponIconImage = Cast<UImage>(GetWidgetFromName(TEXT("WeaponIcon")));
	SelectedMaskImage = Cast<UImage>(GetWidgetFromName(TEXT("SelectedMask")));
	ThisButton = Cast<UButton>(GetWidgetFromName(TEXT("BackButton")));
	if (SelectedMaskImage) SelectedMaskImage->SetVisibility(ESlateVisibility::Hidden);
}

void UWeaponSlot::SetAsUsingWeapon(FEquippedWeaponSet EquippedWeapon)
{
	this->IndexInRole = -1;
	this->Weapon = EquippedWeapon;
	WeaponUIInfo = FThingUIInfo::AsWeaponObj(-1, Weapon.WeaponType);
	Draw();
}

void UWeaponSlot::SetAsWeaponInBackpack(int Index)
{
	if (Index < 0) Index = 0;
	if (Index > UAwGameInstance::Instance->RoleInfo.WeaponObjs.Num()) Index = UAwGameInstance::Instance->RoleInfo.WeaponObjs.Num() - 1;
	this->IndexInRole = Index;
	if (Index < 0)
	{
		Weapon = FEquippedWeaponSet();
		WeaponUIInfo = FThingUIInfo();
	}else
	{
		//Weapon = UAwGameInstance::Instance->RoleInfo.WeaponObjs[Index];
		WeaponUIInfo = FThingUIInfo::AsWeaponObj(IndexInRole, Weapon.WeaponType);
	}
	if (this->ThisButton)
	{
		this->ThisButton->OnClicked.AddDynamic(this, &UWeaponSlot::TryWearThisWeapon);
	}
	Draw();
}

void UWeaponSlot::Draw()
{
	if (WeaponIconImage && this->WeaponUIInfo.Icon.IsEmpty() == false)
	{
		UTexture2D* IconTexture = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath( this->WeaponUIInfo.Icon));
		if (IconTexture)
		{
			WeaponIconImage->SetBrushFromTexture(IconTexture);
			WeaponIconImage->SetVisibility(ESlateVisibility::HitTestInvisible);
		}else
		{
			WeaponIconImage->SetVisibility(ESlateVisibility::Hidden);
		}
	}
}

void UWeaponSlot::NativeOnListItemObjectSet(UObject* ListItemObject)
{
	// IUserObjectListEntry::NativeOnListItemObjectSet(ListItemObject);

	const UWeaponSlot* WSlot = Cast<UWeaponSlot>(ListItemObject);
	this->Weapon = WSlot->Weapon;
	this->WeaponUIInfo = WSlot->WeaponUIInfo;
	this->IndexInRole = WSlot->IndexInRole;
	this->ThisButton->OnClicked = WSlot->ThisButton->OnClicked;
	
	Draw();
}

void UWeaponSlot::NativeOnItemSelectionChanged(bool bIsSelected)
{
	SelectedMaskImage->SetVisibility(
		bIsSelected ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Hidden
	);
	if (bIsSelected)
		UKismetSystemLibrary::PrintString(this, "[" + this->WeaponUIInfo.Name +"] Has Been Selected");
}

void UWeaponSlot::TryWearThisWeapon()
{
	AAwCharacter* Me = UGameplayFuncLib::GetAwGameState()->GetMyCharacter();
	if (Me && this->IndexInRole >= 0)
	{
		//Me->WearWeapon(UAwGameInstance::Instance->RoleInfo.WeaponObjs[IndexInRole]);
	}
}