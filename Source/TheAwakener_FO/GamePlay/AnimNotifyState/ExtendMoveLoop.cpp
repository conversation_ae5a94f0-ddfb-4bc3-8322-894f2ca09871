// Fill out your copyright notice in the Description page of Project Settings.


#include "ExtendMoveLoop.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UExtendMoveLoop::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyEnd(MeshComp, Animation, EventReference);
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;

	const UAnimMontage* Montage = Me->GetCurrentActiveMontage();
	FAnimMontageInstance* MontageInstance = Me->GetActiveMontageInstance();
	
	if (Montage && MontageInstance)
	{
		bool FoundTarget = false;
		for (TTuple<AAwCharacter*, FString> ACha : UGameplayFuncLib::GetAwGameState()->AllCharacters)
		{
			if (ACha.Key && ACha.Key->Dead() == false && Me->IsEnemy(ACha.Key) &&
				FVector::Distance(Me->GetActorLocation(), ACha.Key->GetActorLocation()) <= this->FindEnemyDistance
			){
				FoundTarget = true;
				break;
			}
		}
		if (
			FoundTarget == true ||
			(Me->ExtendMoveInfo.Distance <= 0 && this->Distance == true) ||
			(Me->ExtendMoveInfo.Duration <= 0 && this->Duration == true)		
		)
		{
			//发现了要去终点了
			const int32 SectionID = Montage->GetSectionIndex(FName(this->ExtendEndJumpToSection));
			if (SectionID != INDEX_NONE)
			{
				float StartTime = 0.f;
				float EndTime = 0.f;
				Montage->GetSectionStartAndEndTime(SectionID, StartTime, EndTime);
				MontageInstance->SetPosition(StartTime); 
			}
		}else
		{
			//跳回Loop开始
			const int32 SectionID = Montage->GetSectionIndex(FName(this->ExtendLoopAnimStartSection));
			if (SectionID != INDEX_NONE)
			{
				float StartTime = 0.f;
				float EndTime = 0.f;
				Montage->GetSectionStartAndEndTime(SectionID, StartTime, EndTime);
				MontageInstance->SetPosition(StartTime); 
			}
		}
	}
	
}

void UExtendMoveLoop::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyBegin(MeshComp, Animation, TotalDuration, EventReference);
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	Me->ExtendMoveInfo.Distance = this->MaxDistance;
	Me->ExtendMoveInfo.Duration = this->MaxDuration;
}

void UExtendMoveLoop::NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyTick(MeshComp, Animation, FrameDeltaTime, EventReference);
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;

	const UAnimMontage* Montage = Me->GetCurrentActiveMontage();
	FAnimMontageInstance* MontageInstance = Me->GetActiveMontageInstance();
	
	if (Montage && MontageInstance)
	{
		bool FoundTarget = false;
		for (TTuple<AAwCharacter*, FString> ACha : UGameplayFuncLib::GetAwGameState()->AllCharacters)
		{
			if (ACha.Key && ACha.Key->Dead() == false && Me->IsEnemy(ACha.Key) &&
				FVector::Distance(Me->GetActorLocation(), ACha.Key->GetActorLocation()) <= this->FindEnemyDistance
			){
				FoundTarget = true;
				break;
			}
		}
		Me->ExtendMoveInfo.Distance -= Me->GetMoveComponent()->ThisTickXYMoved;
		Me->ExtendMoveInfo.Duration -= FrameDeltaTime;
		if (
			FoundTarget == true ||
			(Me->ExtendMoveInfo.Distance <= 0 && this->Distance == true) ||
			(Me->ExtendMoveInfo.Duration <= 0 && this->Duration == true)		
		)
		{
			//发现了要去终点了
			const int32 SectionID = Montage->GetSectionIndex(FName(this->ExtendEndJumpToSection));
			if (SectionID != INDEX_NONE)
			{
				float StartTime = 0.f;
				float EndTime = 0.f;
				Montage->GetSectionStartAndEndTime(SectionID, StartTime, EndTime);
				MontageInstance->SetPosition(StartTime); 
			}
		}
		//Tick 没有else一说，end要判断跳回去
	}

}


