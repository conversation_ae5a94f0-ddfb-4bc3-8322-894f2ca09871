// The copyright of this project belongs to Shanghai Minghuan Software Technology Co., Ltd


#include "AwGameViewportClient.h"
#include "InputKeyEventArgs.h"
#include "Engine/GameViewportClient.h"
#include "Engine/GameInstance.h"
#include "InputCoreTypes.h"
#include "InputKeyEventArgs.h"
#include "Engine/World.h"
#include "Misc/CoreMiscDefines.h"
#include "GenericPlatform/GenericPlatformInputDeviceMapper.h"

void UAwGameViewportClient::RemapControllerInput(FInputKeyEventArgs& InOutKeyEvent)
{
	Super::RemapControllerInput(InOutKeyEvent);

	const int32 NumLocalPlayers = World ? World->GetGameInstance()->GetNumLocalPlayers() : 0;
    // 当有两个玩家的时，且输入时来之手柄
	if (NumLocalPlayers > 1 )
	{
		if (InOutKeyEvent.Key.IsGamepadKey())
		{
			if (bDisableSwapGamepadDevice)
			{
				return;
			}
		
			// 我们知道第一个手柄的ID为0
			if (InOutKeyEvent.InputDevice.GetId() == 0)
			{
				// 修改ID为1
				InOutKeyEvent.InputDevice = FInputDeviceId::CreateFromInternalId(0);

				IPlatformInputDeviceMapper& PlatformInputDeviceMapper = IPlatformInputDeviceMapper::Get();

				FPlatformUserId OwningUser = PlatformInputDeviceMapper.GetUserForInputDevice(InOutKeyEvent.InputDevice);

				// 当只插入一个手柄的时候，ID为1的设备是不存在的，因此需要我们手动去创建一个，并映射到第二个LocalPlayer上，下次就不需要创建了
				if (!OwningUser.IsValid())
				{
					UE_LOG(LogTemp, Warning, TEXT("No second gamepad input device detected, attempting to generate a placeholder for the second device for switching."))
					OwningUser = FPlatformUserId::CreateFromInternalId(0);
					PlatformInputDeviceMapper.Internal_MapInputDeviceToUser(InOutKeyEvent.InputDevice, OwningUser, EInputDeviceConnectionState::Connected);
				}
			}
			else if (InOutKeyEvent.InputDevice.GetId() == 1)
			{
				InOutKeyEvent.InputDevice = FInputDeviceId::CreateFromInternalId(1);
			}
		}
		else
		{
			InOutKeyEvent.InputDevice = FInputDeviceId::CreateFromInternalId(1);

			IPlatformInputDeviceMapper& PlatformInputDeviceMapper = IPlatformInputDeviceMapper::Get();

			FPlatformUserId OwningUser = PlatformInputDeviceMapper.GetUserForInputDevice(InOutKeyEvent.InputDevice);

			// 当只插入一个手柄的时候，ID为1的设备是不存在的，因此需要我们手动去创建一个，并映射到第二个LocalPlayer上，下次就不需要创建了
			if (!OwningUser.IsValid())
			{
				UE_LOG(LogTemp, Warning, TEXT("No second gamepad input device detected, attempting to generate a placeholder for the second device for switching."))
				OwningUser = FPlatformUserId::CreateFromInternalId(1);
				PlatformInputDeviceMapper.Internal_MapInputDeviceToUser(InOutKeyEvent.InputDevice, OwningUser, EInputDeviceConnectionState::Connected);
			}
		}
	}
}