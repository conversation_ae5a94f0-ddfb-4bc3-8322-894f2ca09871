// 


#include "HideBubbleWidget.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"


void UHideBubbleWidget::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);
	
	AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Character) return;

	Character->HideBubbleWidget(bHide);
}
