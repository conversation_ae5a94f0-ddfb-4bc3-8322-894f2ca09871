// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "Dom/JsonObject.h"
#include "CoreMinimal.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "RogueMapConfig.generated.h"

/**
 * 
 */
UENUM(BlueprintType)
enum class ERogueRoomReward:uint8
{
	Relic,
	Item,
	Action,
	Soul,
	Coin
};

UENUM(BlueprintType)
enum class ERogueRoomType:uint8
{
	Normal,
	Elite,
	Boss,
	Challenge,
	Store,
	Upgrade
};

USTRUCT(BlueprintType)
struct FRogueRoomStepInfo
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadOnly)
	int RoomStep = 0;
	UPROPERTY(BlueprintReadOnly)
	TMap<ERogueRoomType,int> CanUseRewardType;

	static FRogueRoomStepInfo FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FRogueRoomStepInfo Res = FRogueRoomStepInfo();
		
		Res.RoomStep = UDataFuncLib::AwGetNumberField(JsonObj, "StepIndex", 0);

		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> Type : UDataFuncLib::AwGetArrayField(JsonObj, "RoomType"))
		{
			int Weight = 1;
			FString TypeString = Type->AsString();
			if (TypeString.Contains("(")&&TypeString.Contains(")"))
			{
				FString Left;
				FString Right;
				FString StackString = UDataFuncLib::SplitParamBetweenSplitChar(TypeString,"(",")",Left,Right);
				Weight = FCString::Atoi(*StackString);
				FString RightString;
				TypeString.Split("(",&TypeString ,&RightString);
			}
			Res.CanUseRewardType.Add(UDataFuncLib::FStringToEnum<ERogueRoomType>(TypeString), Weight);
		}
		
		return Res;
	}
};

USTRUCT(BlueprintType)
struct FRogueRoomLevelInfo
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite)
	FString RoomName = "";
	UPROPERTY(BlueprintReadWrite)
	TArray<FString> RoomLevelPath;
	UPROPERTY(BlueprintReadWrite)
	ERogueRoomType RoomType = ERogueRoomType::Normal;
	UPROPERTY(BlueprintReadWrite)
	TArray<ERogueRoomReward> RoomRewards;
	UPROPERTY(BlueprintReadWrite)
	TArray<AActor*> MobSpawnPoint;

	static FRogueRoomLevelInfo FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FRogueRoomLevelInfo Res = FRogueRoomLevelInfo();
		
		Res.RoomName = UDataFuncLib::AwGetStringField(JsonObj, "RoomName", "");

		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> LevelPath : UDataFuncLib::AwGetArrayField(JsonObj, "RoomLevelPath"))
		{
			Res.RoomLevelPath.Add(LevelPath->AsString());
		}

		Res.RoomType = UDataFuncLib::FStringToEnum<ERogueRoomType>(UDataFuncLib::AwGetStringField(JsonObj, "RoomType", "Normal"));
		
		return Res;
	}
};

USTRUCT(BlueprintType)
struct FRogueLevelInfo
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite)
	FString LevelName = "";
	UPROPERTY(BlueprintReadWrite)
	FString LevelPath = "";
	UPROPERTY(BlueprintReadOnly)
	TArray<FRogueRoomLevelInfo> RoomList;

	static FRogueLevelInfo FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FRogueLevelInfo Res = FRogueLevelInfo();
		
		Res.LevelName = UDataFuncLib::AwGetStringField(JsonObj, "LevelName", "");

		Res.LevelPath = UDataFuncLib::AwGetStringField(JsonObj, "LevelPath", "");

		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> AppearInfo : UDataFuncLib::AwGetArrayField(JsonObj, "RoomList"))
		{
			Res.RoomList.Add(FRogueRoomLevelInfo::FromJson(AppearInfo->AsObject()));
		}
		
		return Res;
	}
};

USTRUCT(BlueprintType)
struct FRogueRoomInfo
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite)
	ERogueRoomType RoomType = ERogueRoomType::Normal;
	UPROPERTY(BlueprintReadWrite)
	TArray<ERogueRoomReward> RoomReward;
	UPROPERTY(BlueprintReadWrite)
	int RoomLevel = 0;
	UPROPERTY(BlueprintReadWrite)
	int RoomStep = 0;
	UPROPERTY(BlueprintReadWrite)
	FString RoomName = "";
};

USTRUCT(BlueprintType)
struct FRogueRoomWaveGroup
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite)
	FString GroupId = "";
	//<MobId,MobNum>
	UPROPERTY(BlueprintReadWrite)
	TMap<FString,int> MobList;

	static FRogueRoomWaveGroup FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FRogueRoomWaveGroup Res = FRogueRoomWaveGroup();
		
		Res.GroupId = UDataFuncLib::AwGetStringField(JsonObj, "GroupId", "");

		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> MobInfo : UDataFuncLib::AwGetArrayField(JsonObj, "MobList"))
		{
			FString MobId = UDataFuncLib::AwGetStringField(MobInfo->AsObject(), "MobId", "");
			int MobNum = UDataFuncLib::AwGetNumberField(MobInfo->AsObject(), "MobNum", 0);
			Res.MobList.Add(MobId, MobNum);
		}
		return Res;
	}
};

USTRUCT(BlueprintType)
struct FRogueRoomWaveInfo
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite)
	FString GroupId = "";
	UPROPERTY(BlueprintReadWrite)
	TArray<FString> UsePointTags;
	UPROPERTY(BlueprintReadWrite)
	int NextWaveMobNum = 0;
};

USTRUCT(BlueprintType)
struct FRogueRoomMobInfo
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite)
	FString LevelName = "";
	UPROPERTY(BlueprintReadWrite)
	FString RoomId = "";
	UPROPERTY(BlueprintReadWrite)
	TArray<int> CurseLevel;
	UPROPERTY(BlueprintReadWrite)
	TArray<FRogueRoomWaveInfo> Waves;

	static FRogueRoomMobInfo FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FRogueRoomMobInfo Res = FRogueRoomMobInfo();
		
		Res.LevelName = UDataFuncLib::AwGetStringField(JsonObj, "LevelName", "");

		Res.RoomId = UDataFuncLib::AwGetStringField(JsonObj, "RoomId", "");

		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> Num : UDataFuncLib::AwGetArrayField(JsonObj, "CurseLevel"))
		{
			Res.CurseLevel.Add(Num->AsNumber());
		}
		if(Res.CurseLevel.Num() <= 0)
			Res.CurseLevel.Add(0);

		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> WaveInfo : UDataFuncLib::AwGetArrayField(JsonObj, "Waves"))
		{
			FRogueRoomWaveInfo NewWave = FRogueRoomWaveInfo();
			NewWave.GroupId = UDataFuncLib::AwGetStringField(WaveInfo->AsObject(), "GroupId", "");
			
			NewWave.NextWaveMobNum = UDataFuncLib::AwGetNumberField(WaveInfo->AsObject(), "NextWaveMobNum", 0);

			TArray<FString> UsePointTags;

			for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> UsePointTag : UDataFuncLib::AwGetArrayField(WaveInfo->AsObject(), "UsePointTags"))
			{
				NewWave.UsePointTags.Add(UsePointTag->AsString());
			}
			Res.Waves.Add(NewWave);
		}
		return Res;
	}
};