// Fill out your copyright notice in the Description page of Project Settings.


#include "CalculationOfCurvesByTargetPoint.h"

#include "Animation/AnimNotifyEndDataContext.h"
#include "Components/SkeletalMeshComponent.h"
#include "Kismet/KismetMathLibrary.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"



void UCalculationOfCurvesByTargetPoint::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	float TotalDuration, const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyBegin(MeshComp, Animation, TotalDuration, EventReference);
	AAwCharacter* OwnerCharacter = Cast<AAwCharacter>(MeshComp->GetOwner());

	if(OwnerCharacter)
	{
		OriginTransform = OwnerCharacter->GetActorTransform();
		if (OwnerCharacter->GetAwAnimInstance()->CurMontageUID ==UID||OwnerCharacter->GetAwAnimInstance()->CurMontageUID.IsEmpty())
		{
			return;
		}
		UID = OwnerCharacter->GetAwAnimInstance()->CurMontageUID;
		TimeElapsed = 0;
		if(!bRegularHorizontalSpeed)
		{
			FVector TargetLoc = OwnerCharacter->GetAIComponent()->GetTargetLocation();
			FVector Loc = OriginTransform.GetLocation();
			const float ToFlyLen = (TargetLoc - Loc).Size2D();
			ActualHorizontalSpeed = ToFlyLen / TotalDuration;
		}
		else
		{
			ActualHorizontalSpeed = ExpectedHorizontalSpeed;
		}
			
	}
}

void UCalculationOfCurvesByTargetPoint::NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	float FrameDeltaTime, const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyTick(MeshComp,Animation ,FrameDeltaTime, EventReference);

	AAwCharacter* OwnerCharacter = Cast<AAwCharacter>(MeshComp->GetOwner());

	if(OwnerCharacter)
	{
		
		TimeElapsed += FrameDeltaTime;
		FVector TargetLoc = OwnerCharacter->GetAIComponent()->GetTargetLocation();
		FVector Loc = OriginTransform.GetLocation();
		
		const float zOffset = TargetLoc.Z - Loc.Z;
		const float ToFlyLen = (TargetLoc - Loc).Size2D();
		const float NewHeight = UKismetMathLibrary::Clamp((1 - (ExpectedHeightMin / ToFlyLen)) * ExpectedHeight,ExpectedHeightMin,ExpectedHeightMax);
		ExpectedLength = ToFlyLen;
		//ExpectedHorizontalSpeed = UKismetMathLibrary::Clamp(ToFlyLen/2,ExpectedHorizontalSpeedMin,ExpectedHorizontalSpeedMax);
		if (FMath::IsNearlyEqual(ExpectedLength,ToFlyLen))
		{
			HeightLengthRatio = 1;
		}

		const float TopMaxHeight = NewHeight  * HeightLengthRatio * ToFlyLen / ExpectedLength ;

		float TopLen = 0;
		//逆推顶点X坐标

		if (zOffset >= TopMaxHeight)
		{
			TopLen = ToFlyLen;
		}
		else
		{
			float k1 = (-ToFlyLen + sqrt(ToFlyLen * ToFlyLen - ToFlyLen * ToFlyLen * zOffset / TopMaxHeight)) / (ToFlyLen * ToFlyLen / (-2 * TopMaxHeight));
			float k2 = (-ToFlyLen - sqrt(ToFlyLen * ToFlyLen - ToFlyLen * ToFlyLen * zOffset / TopMaxHeight)) / (ToFlyLen * ToFlyLen / (-2 * TopMaxHeight));
			float k = k1 > k2 ? k1 : k2;
			if (k1 < 0 && k2 < 0)
			{
				k = k1 < k2 ? k1 : k2;
			}
			TopLen = 2 * TopMaxHeight / k;
		}

		const float HalfTime = TopLen/ActualHorizontalSpeed;


		float FlyPer = TimeElapsed  * ActualHorizontalSpeed; //当前的x

		FVector2D CurveTopPoint = FVector2D(TopLen,TopMaxHeight);
		//y = ax^2 + bx Top = (-b/2a,b^2/4a) 不用pow 是因为*消耗少
		float x = CurveTopPoint.X;
		float y = CurveTopPoint.Y;
		float a =  -y/ (x * x);
		float b = 2 * y/ x;

		float CurTargetZ = a * FlyPer * FlyPer + b * FlyPer;
		float CurZ = OwnerCharacter->GetActorLocation().Z - OriginTransform.GetLocation().Z;


		FVector2D RealTopPoint = FVector2D();


		// 一定区域内 修改抛物线斜率以平滑Z方向速度
		if (FlyPer<ToFlyLen && FlyPer> TopLen* DragLenPer&& FlyPer < TopLen * (2-DragLenPer))
		{
			float LimitZ = 0;
			if (ToFlyLen> TopLen * (2 - DragLenPer))
			{
				LimitZ = a * TopLen * DragLenPer * TopLen * DragLenPer + b * TopLen * DragLenPer;
			}
			else
			{
				LimitZ = zOffset;
			}
			CurTargetZ = FMath::Lerp(LimitZ, CurTargetZ, DragSpeedPer);
		}

		FVector Res = FVector::ZeroVector;
		Res.X =  FlyPer;
		Res.Y = 0;
		Res.Z = CurTargetZ;
		
		FVector Temp = UKismetMathLibrary::TransformLocation(OriginTransform,  Res);
		
		OwnerCharacter->SetActorLocation(Temp,true);
	}
}

void UCalculationOfCurvesByTargetPoint::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyEnd(MeshComp, Animation, EventReference);
}
