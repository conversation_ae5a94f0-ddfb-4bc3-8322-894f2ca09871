#pragma once

#include "CoreMinimal.h"
#include "Components/Widget.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "Widgets/Layout/Anchors.h"
#include "UIFuncLib.generated.h"
UCLASS()
class THEAWAKENER_FO_API UIFuncLib : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()
public:
	UFUNCTION(BlueprintCallable, Category="UI")
	static void UpdateAnchorPreservingVisual(UWidget* Widget, const FAnchors& NewAnchors);
};
