// Fill out your copyright notice in the Description page of Project Settings.


#include "ItemShortcutBarUI.h"

#include "TimerManager.h"
#include "Engine/Texture2D.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void UItemShortcutBarUI::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	GameState		=	UGameplayFuncLib::GetAwGameState();

	ItemList		=	Cast<UMenuList>(GetWidgetFromName("WBP_MenuList"));
	ItemPropsUI_0	=	Cast<UItemPropsUI>(GetWidgetFromName("WBP_ItemProps0"));
	ItemPropsUI_1	=	Cast<UItemPropsUI>(GetWidgetFromName("WBP_ItemProps1"));
	ItemPropsUI_2	=	Cast<UItemPropsUI>(GetWidgetFromName("WBP_ItemProps2"));
	ItemPropsUI_3	=	Cast<UItemPropsUI>(GetWidgetFromName("WBP_ItemProps3"));
	ItemExplainUI	=	Cast<UExplainUI>(GetWidgetFromName("WBP_Explain"));

	ShortcutBarItems.Add(ItemPropsUI_0);
	ShortcutBarItems.Add(ItemPropsUI_1);
	ShortcutBarItems.Add(ItemPropsUI_2);
	ShortcutBarItems.Add(ItemPropsUI_3);



	GetWorld()->GetTimerManager().SetTimer(TimerHandle,this,&UItemShortcutBarUI::SetGameUIControllState,0.3f,true);
}

void UItemShortcutBarUI::NativeConstruct()
{
	Super::NativeConstruct();

	this->SetRenderOpacity(0);
	GetWorld()->GetTimerManager().SetTimer(TimerHandle1,this,&UItemShortcutBarUI::InitItemShortcutBar,0.15f,true);
}

void UItemShortcutBarUI::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);

	if (GameState)
	{
		AAwCharacter* Me = GameState->GetMyCharacter();
		if(UGameplayFuncLib::GetWorkingAwPlayerController()->GameUIControlState == EGameUIControlState::SecondaryUIState)
		{
			if(Me->GetCmdComponent()->IsActionOccur("Menu_Up") || Me->GetCmdComponent()->IsActionOccur("Menu_Up",EAwInputState::Hold))
			{
				if(ItemList->MenuList->GetNumItems())
				{
					ItemList->GetSelectListEntry()->EntryShadeImage->SetVisibility(ESlateVisibility::Collapsed);
					ChangeShortcutBarPlate("ShortcutBarPlate_NotHover");
					ItemList->ListSelectUp();
					ChangeShortcutBarPlate("ShortcutBarPlate_Hover");
					ItemList->GetSelectListEntry()->EntryShadeImage->SetVisibility(ESlateVisibility::Visible);
					UpdateView();
				}
			}
		
			else if(Me->GetCmdComponent()->IsActionOccur("Menu_Down") || Me->GetCmdComponent()->IsActionOccur("Menu_Down",EAwInputState::Hold))
			{
				if(ItemList->MenuList->GetNumItems())
				{
					ItemList->GetSelectListEntry()->EntryShadeImage->SetVisibility(ESlateVisibility::Collapsed);
					ChangeShortcutBarPlate("ShortcutBarPlate_NotHover");
					ItemList->ListSelectDown();
					ChangeShortcutBarPlate("ShortcutBarPlate_Hover");
					ItemList->GetSelectListEntry()->EntryShadeImage->SetVisibility(ESlateVisibility::Visible);
					UpdateView();
				}
			}
		
			if(Me->GetCmdComponent()->IsActionOccur("Menu_Confirm",EAwInputState::Press,true))
			{
				if(ItemList->GetSelectData())
				{
					if(ItemList->GetSelectData()->IsEquipped)
					{
						UGameplayFuncLib::PlayUIAudio("ConfirmKey_No");
					}
					else
					{
						UGameplayFuncLib::PlayUIAudio("ConfirmKey_Yes");
					}
					SetQuickProps();
					UpdateView();
				}
			}
		
			if(Me->GetCmdComponent()->IsActionOccur("Menu_Refuse",EAwInputState::Press,true))
			{
				UGameplayFuncLib::GetWorkingAwPlayerController()->GameUIControlState = EGameUIControlState::Changing;
				UGameplayFuncLib::PlayUIAudio("ConfirmKey_Back");
				SetIsSelfCloss(false);
				Back();
			}
		}
		UpdateAnimPlay();
	}
}

void UItemShortcutBarUI::SetGameUIControllState()
{
	UGameplayFuncLib::GetWorkingAwPlayerController()->GameUIControlState = EGameUIControlState::SecondaryUIState;
	GetWorld()->GetTimerManager().ClearTimer(TimerHandle);
}


void UItemShortcutBarUI::UpdateView()
{
	const UMenuListEntryData* TempData = ItemList->GetSelectData();
	FAwRoleInfo RoleInfo = UGameplayFuncLib::GetAwGameInstance()->RoleInfo;
	
	if(TempData)
	{
		UTexture2D* TempIcon = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(UGameplayFuncLib::GetAwDataManager()->
			GetBaseThingUIInfo(EThingType::Item,ItemList->GetSelectData()->ItemId).Icon));
		if(TempIcon)
		{
			ItemExplainUI->SetAttribute(
			TempIcon,
			TempData->Name,
			UGameplayFuncLib::GetAwDataManager()->GetTextByKey(TempData->ItemId  + "Explain"),
			RoleInfo.GetItemObjCount(TempData->ItemId));
			ItemExplainUI->Explain_Icon->SetDesiredSizeOverride(FVector2D(TempIcon->GetSizeX(),TempIcon->GetSizeY()));
		}
	}
}

void UItemShortcutBarUI::Back()
{
	if(GetIsSelfCloss())
	{
		PlayWidgetAnim("ShowAllItemShortcutBar",0.0f,1,EUMGSequencePlayMode::Reverse,1.0f,false);
	}
	else
	{
		PlayWidgetAnim("ShowItemShortcutBar",0.0f,1,EUMGSequencePlayMode::Reverse,1.0f,false);
	}
	
	SetIsAnimPlay(true);
}

void UItemShortcutBarUI::SetQuickProps()
{
	UMenuListEntry* TemEntry = Cast<UMenuListEntry>(ItemList->MenuList->
			GetEntryWidgetFromItem(ItemList->GetSelectData()));
	FAwRoleInfo RoleInfo = UGameplayFuncLib::GetAwGameInstance()->RoleInfo;
	
	if(TemEntry)
	{
		if(ItemList->GetSelectData()->IsEquipped)
		{
			ChangeShortcutBarPlate("ShortcutBarPlate_NotHover");
			ShortcutBarItems[ItemList->GetSelectData()->Index]->SetIsHaveQuickProps(false);
			ShortcutBarItems[ItemList->GetSelectData()->Index]->SetQuickPropId("");
			ItemList->GetSelectData()->IsEquipped = false;
			TemEntry->MakeIconOverlay->SetVisibility(ESlateVisibility::Collapsed);
			UGameplayFuncLib::GetAwGameInstance()->RoleInfo.QuickSlotItemId[ItemList->GetSelectData()->Index] = "";
		}
		else
		{
			for (int i = 0;i < ShortcutBarItems.Num();++i)
			{
				if(!ShortcutBarItems[i]->GetIsHaveQuickProps())
				{
					ShortcutBarItems[i]->SetIsHaveQuickProps(true);
					ShortcutBarItems[i]->SetQuickPropId(ItemList->GetSelectData()->ItemId);
					ItemList->GetSelectData()->Index = i;
					ItemList->GetSelectData()->IsEquipped = true;
					TemEntry->SetEntryMakerBox(UGameplayFuncLib::GetAwDataManager()->
						GetItemIconById(FString::FromInt(i + 1)).Path);
					UGameplayFuncLib::GetAwGameInstance()->RoleInfo.QuickSlotItemId[i] = ItemList->GetSelectData()->ItemId;
					break;
				}
			}
			ChangeShortcutBarPlate("ShortcutBarPlate_Hover");
			TemEntry->MakeIconOverlay->SetVisibility(ESlateVisibility::Visible);
		}
		
		TemEntry->UpdateView();
		ShortcutBarItems[ItemList->GetSelectData()->Index]->SetAttribute(UGameplayFuncLib::GetAwDataManager()->
			GetBaseThingUIInfo(EThingType::Item,ItemList->GetSelectData()->ItemId).Icon);
		ShortcutBarItems[ItemList->GetSelectData()->Index]->UpdateView();
	}
	
}

void UItemShortcutBarUI::UpdateAnimPlay()
{
	if(GetIsAnimPlay())
	{
		if(GetIsSelfCloss())
		{
			UWidgetAnimation* TemAnim = GetNameWidgetAnimation("ShowAllItemShortcutBar");
			if(TemAnim)
			{
				if(!IsAnimationPlaying(TemAnim))
				{
					SetIsAnimPlay(false);
					
					UGameplayFuncLib::GetWorkingAwPlayerController()->GameControlState = EGameControlState::Game;
					
					UGameplayFuncLib::GetUiManager()->Hide("ItemShortcutBar");
				}
			}
		}
		else
		{
			UWidgetAnimation* TemAnim = GetNameWidgetAnimation("ShowItemShortcutBar");
			if(TemAnim)
			{
				if(!IsAnimationPlaying(TemAnim))
				{
					SetIsAnimPlay(false);
				
					UGameMenu* GameMenu = Cast<UGameMenu>(UGameplayFuncLib::GetUiManager()->Show("Menu",9999));
					GameMenu->GetMenuList()->SetEntry(UGameplayFuncLib::GetDataManager()->GetListItemsById("Menu"));
					FListItemElementInfo TemItemElementInfo = UGameplayFuncLib::GetAwDataManager()->GetListItemsById("Menu");
					for (int i = 0;i < TemItemElementInfo.Items.Num();++i)
					{
						TemItemElementInfo.Items[i].Name = UGameplayFuncLib::GetAwDataManager()->GetTextByKey(TemItemElementInfo.Items[i].Id);
					}
					GameMenu->GetMenuList()->SetEntry(TemItemElementInfo);
					GameMenu->SetListInitScroll(GetMainIndex());
					UGameplayFuncLib::GetWorkingAwPlayerController()->GameControlState = EGameControlState::PauseMenu;
					
					UGameplayFuncLib::GetUiManager()->Hide("ItemShortcutBar");
				}
			}
		}
	}
}

void UItemShortcutBarUI::InitItemShortcutBar()
{
	if(IsValid(ItemList->GetSelectListEntry()))
		ItemList->GetSelectListEntry()->EntryShadeImage->SetVisibility(ESlateVisibility::Visible);
	if(ItemList->MenuList->GetNumItems() > 0)
	{
		for(int i = 0; i < ItemList->MenuList->GetNumItems(); ++i)
		{
			UMenuListEntry* TempEntry = ItemList->MenuList->
			GetEntryWidgetFromItem<UMenuListEntry>(ItemList->MenuList->GetItemAt(i));
			if(TempEntry)
			{
				TempEntry->PlayWidgetAnim("EffectRespiration",0.0f,0,EUMGSequencePlayMode::Forward,1.0f,false);
				TempEntry->EntryMakerBox->SetDesiredSizeOverride(FVector2D(TempEntry->EntryMakerBox->GetBrush().GetImageSize().X * 0.2f,
					TempEntry->EntryMakerBox->GetBrush().GetImageSize().Y * 0.2f));
			}
			else
			{
				TempEntry = ItemList->MenuList->
				GetEntryWidgetFromItem<UMenuListEntry>(ItemList->MenuList->GetItemAt(i));
				if(TempEntry)
				{
					TempEntry->PlayWidgetAnim("EffectRespiration",0.0f,0,EUMGSequencePlayMode::Forward,1.0f,false);
					TempEntry->EntryMakerBox->SetDesiredSizeOverride(FVector2D(TempEntry->EntryMakerBox->GetBrush().GetImageSize().X * 0.2f,
						TempEntry->EntryMakerBox->GetBrush().GetImageSize().Y * 0.2f));
				}
			}
		}
		ChangeShortcutBarPlate("ShortcutBarPlate_Hover");
	}

	GetWorld()->GetTimerManager().ClearTimer(TimerHandle1);

	this->SetRenderOpacity(1);
	PlayWidgetAnim("ShowItemShortcutBar",0.0f,1,EUMGSequencePlayMode::Forward,1.0f,false);
}

void UItemShortcutBarUI::ChangeShortcutBarPlate(FString Name)
{
	if(IsValid(ItemList->GetSelectListEntry()) && ItemList->GetSelectListEntry()->GetEntryData()->IsEquipped)
	{
		UTexture2D* TempTexture = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(
			UGameplayFuncLib::GetAwDataManager()->GetItemIconById(Name).Path));
		if(TempTexture)
			ShortcutBarItems[ItemList->GetSelectListEntry()->GetEntryData()->Index]->ItemProps_Ground->
			SetBrushFromTexture(TempTexture);
	}
}
