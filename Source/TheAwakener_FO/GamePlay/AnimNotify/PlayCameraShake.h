// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotify.h"
#include "TheAwakener_FO/GamePlay/Camera/AwCameraShakeInfo.h"
#include "PlayCameraShake.generated.h"

/**
 * 在所在位置震屏
 */
UCLASS()
class THEAWAKENER_FO_API UPlayCameraShake : public UAnimNotify
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FAwCameraShakeInfo ShakeInfo;

	virtual void Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
