// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/SceneComponent.h"
#include "FXPlayPoint.generated.h"

UENUM()
enum class EVFXBindPointType : uint8
{
	//头
	Head,
	//眼睛
	Eye,
	//嘴巴
	Mouth,
	//身体
	Body,
	//Root
	Root,
	//
	Mesh
};

/**
 * 播放各种特效的绑点
 */
UCLASS(ClassGroup="Actor Point", editinlinenew, hidecategories=(Object,LOD,Lighting,TextureStreaming), meta=(DisplayName="Play VFX Bind Point", BlueprintSpawnableComponent))
class THEAWAKENER_FO_API UFXPlayPoint : public USceneComponent
{
	GENERATED_BODY()
public:
	//点的类型
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	EVFXBindPointType PointType = EVFXBindPointType::Body;
};
