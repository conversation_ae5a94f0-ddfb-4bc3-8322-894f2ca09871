// Fill out your copyright notice in the Description page of Project Settings.


#include "PriceView.h"
#include "Engine/Texture2D.h"
#include "TheAwakener_FO/FunctionLibrary/ResourceFuncLib.h"

void UPriceView::NativeOnInitialized()
{
	Super::NativeOnInitialized();
	Icon = Cast<UImage>(GetWidgetFromName(TEXT("IconView")));
	Price = Cast<UTextBlock>(GetWidgetFromName(TEXT("PriceView")));
}

void UPriceView::NativeOnListItemObjectSet(UObject* ListItemObject)
{
	// IUserObjectListEntry::NativeOnListItemObjectSet(ListItemObject);

	const UPriceView* PriceView = Cast<UPriceView>(ListItemObject);
	this->Set(PriceView->IconPath, PriceView->PriceValue);
	Draw();
}

void UPriceView::Draw()
{
	if (this->Icon)
	{
		UTexture2D* IconTexture = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(this->IconPath));
		if (IconTexture)
		{
			Icon->SetVisibility(ESlateVisibility::Visible);
			Icon->SetBrushFromTexture(IconTexture);
		}else
		{
			Icon->SetVisibility(ESlateVisibility::Hidden);
		}
	}
	if (Price)
	{
		Price->SetText(FText::FromString(FString::FromInt(PriceValue)));
	}
}

void UPriceView::Set(FString ShowIconPath, int ShowPriceValue)
{
	this->IconPath = ShowIconPath;
	this->PriceValue = ShowPriceValue;
	Draw();
}
