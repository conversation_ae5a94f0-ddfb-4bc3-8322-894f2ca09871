// Fill out your copyright notice in the Description page of Project Settings.


#include "HitGroundDuck.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UHitGroundDuck::NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyTick(MeshComp, Animation, FrameDeltaTime, EventReference);
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me || Me->OnGround() == false || Ducked == true) return;
	this->Ducked = true;
	Me->PreorderActionByMontageState(ECharacterMontageState::Landing);
}
