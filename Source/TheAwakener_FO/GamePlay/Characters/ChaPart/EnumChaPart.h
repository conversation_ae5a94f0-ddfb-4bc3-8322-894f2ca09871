#pragma once

#include "CoreMinimal.h"
#include "EnumChaPart.generated.h"
// 角色部位种类
UENUM(BlueprintType)
enum class EChaPartType: uint8
{
	// 头
	Head,
	// 身体
	Body,
	// 手
	Arm,
	// 脚
	Leg,
	// 翅膀
	Wing,
	// 尾巴
	Tail,
	// 犄角
	Horn,
	// 背
	Back,
	// 一些怪物的盾牌
	Shield,
	//怪物身体上的武器部位
	Weapon,
	//
	Other
};

// 角色肉质类型
UENUM(BlueprintType)
enum class EChaPartMeatType: uint8
{
	// 纯肉的
	Meat,
	//岩石土壤
	Rock,
	// 金属的
	Metal,
	// 虚灵类
	Void,
	// 纯骨头
	Skeleton
};
