// Copyright 2024 JBernardic. All Rights Reserved.

#include "VertexAnimationToolkit.h"
#include "VertexAnimationToolkitStyle.h"
#include "VertexAnimationToolkitCommands.h"
#include "Misc/MessageDialog.h"
#include "ToolMenus.h"

#include "IPersonaToolkit.h"
#include "Rendering/SkeletalMeshModel.h"
#include "Rendering/SkeletalMeshRenderData.h"

#include "IAnimationEditor.h"
#include "Animation/AnimComposite.h"
#include "Animation/DebugSkelMeshComponent.h"

#include "Engine/SkeletalMesh.h"
#include "SkeletalRenderPublic.h"
#include "Runtime/Engine/Private/SkeletalRenderCPUSkin.h"
#include <AssetRegistry/AssetRegistryModule.h>

#include "MeshDescription.h"
#include "StaticMeshAttributes.h"

#include "VertexAnimationUtils.h"
#include <ContentBrowserModule.h>
#include <IContentBrowserSingleton.h>
#include <Materials/MaterialExpressionFunctionInput.h>
#include <Materials/MaterialExpressionConstant.h>
#include <Materials/MaterialExpressionMultiply.h>
#include <Materials/MaterialExpressionFunctionOutput.h>
#include <Materials/MaterialExpressionConstant3Vector.h>
#include <Materials/MaterialExpressionComponentMask.h>
#include <Materials/MaterialExpressionTextureObject.h>
#include <Materials/MaterialExpressionSubtract.h>
#include <Materials/MaterialExpressionClamp.h>
#include <Kismet2/BlueprintEditorUtils.h>

#include <Editor/MaterialEditor/Private/MaterialEditor.h>
#include <Animation/AnimMontage.h>
#include "Animation/AnimInstance.h"
#include <Materials/MaterialExpressionIf.h>
#include <Materials/MaterialExpressionPerInstanceCustomData.h>
#include "UObject/Class.h"
#include "Engine/Texture2DArray.h"

#include <Misc/ScopedSlowTask.h>
#include "ObjectTools.h"
#include "Animation/AnimSequence.h"
#include "Framework/Application/SlateApplication.h"
#include "Materials/MaterialFunction.h"
#include "Widgets/Input/SButton.h"
#include "Widgets/Input/SCheckBox.h"

static const FName VertexAnimationToolkitTabName("Bake VAT");

#define LOCTEXT_NAMESPACE "FVertexAnimationToolkitModule"

void FVertexAnimationToolkitModule::StartupModule()
{
	// This code will execute after your module is loaded into memory; the exact timing is specified in the .uplugin file per-module
	
	FVertexAnimationToolkitStyle::Initialize();
	FVertexAnimationToolkitStyle::ReloadTextures();

	FVertexAnimationToolkitCommands::Register();
	
	PluginCommands = MakeShareable(new FUICommandList);

	PluginCommands->MapAction(
		FVertexAnimationToolkitCommands::Get().PluginAction,
		FExecuteAction::CreateRaw(this, &FVertexAnimationToolkitModule::PluginButtonClicked),
		FCanExecuteAction());

	UToolMenus::RegisterStartupCallback(FSimpleMulticastDelegate::FDelegate::CreateRaw(this, &FVertexAnimationToolkitModule::RegisterMenus));
}

void FVertexAnimationToolkitModule::ShutdownModule()
{
	// This function may be called during shutdown to clean up your module.  For modules that support dynamic reloading,
	// we call this function before unloading the module.

	FCoreDelegates::OnPostEngineInit.RemoveAll(this);

	UToolMenus::UnRegisterStartupCallback(this);

	UToolMenus::UnregisterOwner(this);

	FVertexAnimationToolkitStyle::Shutdown();

	FVertexAnimationToolkitCommands::Unregister();
}

void FVertexAnimationToolkitModule::PluginButtonClicked()
{
	UAnimationAsset* AnimationAsset = Cast<UAnimationAsset>(GetCurrentAsset());
	UDebugSkelMeshComponent* SkelMesh = GetDebugSkelMeshComponent(AnimationAsset);
	if (!SkelMesh)
	{
		FText DialogText = FText::FromString("Unable to find skeletal mesh!");
		FMessageDialog::Open(EAppMsgType::Ok, DialogText);
		return;
	}

	//TOptional<FConfiguration> config = ShowConfigurationModal();
	//if (!config.IsSet()) return;

	FContentBrowserModule& ContentBrowserModule = FModuleManager::LoadModuleChecked<FContentBrowserModule>("ContentBrowser");
	FSaveAssetDialogConfig SaveAssetConfig;
	SaveAssetConfig.DialogTitleOverride = FText::FromString("Save Assets As...");
	SaveAssetConfig.ExistingAssetPolicy = ESaveAssetDialogExistingAssetPolicy::AllowButWarn;
	SaveAssetConfig.DefaultAssetName = AnimationAsset->GetName()+"_VAT";
	FString PathChosen = ContentBrowserModule.Get().CreateModalSaveAssetDialog(SaveAssetConfig);

	if (PathChosen == "")
	{
		return;
	}

	FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
	FAssetData AssetData = AssetRegistryModule.Get().GetAssetByObjectPath(*PathChosen);
	if (AssetData.IsValid())
	{
		if (!(AssetData.AssetClassPath.GetAssetName() == "StaticMesh"))
		{
			FText DialogText = FText::FromString("Error: Choose a different Filename!");
			FMessageDialog::Open(EAppMsgType::Ok, DialogText);
			return;
		}
	}

	FString PackagePath = FPaths::GetPath(PathChosen) + "/";
	FString FileName = FPaths::GetBaseFilename(PathChosen);

	FScopedSlowTask SlowTask(100.0f, FText::FromString("Baking..."));
	SlowTask.MakeDialog();

	TArray<TArray<FVector>> VertPos;
	TArray<TArray<FVector>> VertNormal;
	TArray<FAnimationSection> AnimationSections;
	TArray<int32> VertSize;
	float FrameRate = 0;

	TArray<UTexture2D*> PositionTextures;
	TArray<UTexture2D*> NormalTextures;
	UStaticMesh* StaticMesh = nullptr;

	VertPos.SetNum(SkelMesh->GetNumLODs());
	VertNormal.SetNum(SkelMesh->GetNumLODs());
	VertSize.SetNum(SkelMesh->GetNumLODs());

	SlowTask.EnterProgressFrame(40.0f);
	for (int lod = 0; lod < SkelMesh->GetNumLODs(); ++lod) {
		SetForcedLOD(SkelMesh, lod + 1);

		if (!ExtractVATData(SkelMesh, AnimationAsset, VertPos[lod], VertNormal[lod], VertSize[lod], AnimationSections, FrameRate))
		{
			FText DialogText = FText::FromString("Error while extracting vertex data!");
			FMessageDialog::Open(EAppMsgType::Ok, DialogText);
			return;
		}
	}
	SetForcedLOD(SkelMesh, 0);

	const int FrameCount = VertPos[0].Num() / VertSize[0];

	SlowTask.EnterProgressFrame(20.0f);
	if (!ExportStaticMesh(FrameCount/8192, PackagePath, FileName, SkelMesh, StaticMesh))
	{
		FText DialogText = FText::FromString("Error while creating static mesh!");
		FMessageDialog::Open(EAppMsgType::Ok, DialogText);
		return;
	}



	if (!ExportPlayHelper(PackagePath, FileName + "_PlayHelper", AnimationSections, FrameRate))
	{
		FText DialogText = FText::FromString("Error while generating play helper!");
		FMessageDialog::Open(EAppMsgType::Ok, DialogText);
		return;
	}
	//Have to send notification here due to ExportPlayHelper wiping notifications :(
	FVertexAnimationUtils::PushNotification("Successfully created Static Mesh", StaticMesh);

	SlowTask.EnterProgressFrame(20.0f);
	for (int lod = 0; lod < SkelMesh->GetNumLODs(); ++lod) {
		
		//Support for vert count > 8192
		TArray<int> _VertSizes;
		{
			int _VertSize = VertSize[lod];
			while (_VertSize > 8192) {
				_VertSizes.Push(8192);
				_VertSize -= 8192;
			}
			_VertSizes.Push(_VertSize);
		}

		//Support for frame count > 8192
		TArray<int> _FrameSizes;
		{
			int _FrameSize = FrameCount;
			while (_FrameSize > 8192) {
				_FrameSizes.Push(8192);
				_FrameSize -= 8192;
			}
			_FrameSizes.Push(_FrameSize);
		}

		int VertexOffset = 0;
		for (int CurrentVertexSize : _VertSizes) {
			int FrameOffset = 0;
			for (int CurrentFrameSize : _FrameSizes) {

				TArray<FVector> PositionData;
				TArray<FVector> NormalData;

				PositionData.Reserve(CurrentFrameSize * CurrentVertexSize);
				NormalData.Reserve(CurrentFrameSize * CurrentVertexSize);

				for (int i = FrameOffset; i < FrameOffset + CurrentFrameSize; ++i) {
					for (int j = VertexOffset; j < VertexOffset + CurrentVertexSize; ++j) {
						int index = i * VertSize[lod] + j;
						PositionData.Emplace(VertPos[lod][index]);
						NormalData.Emplace(VertNormal[lod][index]);
					}
				}

				PositionTextures.Add(nullptr);
				NormalTextures.Add(nullptr);
				const int MaxVertSize = VertSize[0] > 8192 ? 8192 : VertSize[0];
				const int MaxFrameCount = FrameCount > 8192 ? 8192 : FrameCount;

				if (!ExportPositionTexture(PositionData, CurrentVertexSize, MaxVertSize, MaxFrameCount, PositionTextures.Last()))
				{
					FText DialogText = FText::FromString("Error while creating position texture!");
					FMessageDialog::Open(EAppMsgType::Ok, DialogText);
					return;
				}
				if (!ExportNormalTexture(NormalData, CurrentVertexSize, MaxVertSize, MaxFrameCount, NormalTextures.Last()))
				{
					FText DialogText = FText::FromString("Error while creating normal texture!");
					FMessageDialog::Open(EAppMsgType::Ok, DialogText);
					return;
				}

				FrameOffset += CurrentFrameSize;
			}
			VertexOffset += CurrentVertexSize;
		}

	}

	SlowTask.EnterProgressFrame(20.0f);
	if (!MergeTextures(PackagePath, FileName + "_Positions", PositionTextures)) {
		FText DialogText = FText::FromString("Error while merging position textures!");
		FMessageDialog::Open(EAppMsgType::Ok, DialogText);
		return;
	}
	if (!MergeTextures(PackagePath, FileName + "_Normals", NormalTextures)) {
		FText DialogText = FText::FromString("Error while merging normal textures!");
		FMessageDialog::Open(EAppMsgType::Ok, DialogText);
		return;
	}
}

void FVertexAnimationToolkitModule::RegisterMenus()
{
	// Owner will be used for cleanup in call to UToolMenus::UnregisterOwner
	FToolMenuOwnerScoped OwnerScoped(this);
	{
		UToolMenu* ToolbarMenu = UToolMenus::Get()->ExtendMenu("AssetEditor.AnimationEditor.ToolBar");
		{
			FToolMenuSection& Section = ToolbarMenu->FindOrAddSection("SkeletalMesh");
			{
				FToolMenuEntry& Entry = Section.AddEntry(FToolMenuEntry::InitToolBarButton(FVertexAnimationToolkitCommands::Get().PluginAction));
				Entry.SetCommandList(PluginCommands);
			}
		}

	}
}

UObject* FVertexAnimationToolkitModule::GetCurrentAsset()
{
	if (GEditor)
	{
		auto assetEditor = GEditor->GetEditorSubsystem<UAssetEditorSubsystem>();
		TArray<UObject*> assets = assetEditor->GetAllEditedAssets();
		double time = 0;
		UObject* asset = nullptr;
		for (UObject* _asset : assets)
		{
			double _time = assetEditor->FindEditorForAsset(_asset, false)->GetLastActivationTime();
			if (_time > time)
			{
				time = _time;
				asset = _asset;
			}
		}
		return asset;

	}
	return nullptr;
}

UDebugSkelMeshComponent* FVertexAnimationToolkitModule::GetDebugSkelMeshComponent(UAnimationAsset* AnimationAsset)
{
	if (!AnimationAsset)
	{
		return nullptr;
	}

	if (GEditor)
	{
		auto AssetEditor = GEditor->GetEditorSubsystem<UAssetEditorSubsystem>();
		auto Editor = AssetEditor->FindEditorForAsset(AnimationAsset, false);
		IAnimationEditor* AnimEditor = static_cast<IAnimationEditor*>(Editor);
		if (AnimEditor)
		{
			return AnimEditor->GetPersonaToolkit()->GetPreviewMeshComponent();
		}
	}

	return nullptr;
}

bool FVertexAnimationToolkitModule::ExtractVATData(UDebugSkelMeshComponent* PreviewMesh, UAnimationAsset* AnimationAsset, TArray<FVector>& OutVertPos, TArray<FVector>& OutVertNormal, int32& OutVertSize, TArray<FAnimationSection>& OutSections, float& OutFrameRate)
{
	OutVertPos.Empty();
	OutVertNormal.Empty();
	OutSections.Empty();
	OutVertSize = 0;

	bool IsCPUSkinning = false;
	//CPU skinning
	{
		IsCPUSkinning = PreviewMesh->GetCPUSkinningEnabled(); //Will be using this to revert later.
		PreviewMesh->SetCPUSkinningEnabled(true, true);
	}
	
	double FrameRate = 30;
	double MaxFrameRate = 30;//减少采样帧率

	struct _Section
	{
		double Start;
		double End;
		FString Name;
		_Section(double Start, double End, FString Name) : Start(Start), End(End), Name(Name)
		{}
	};

	TArray<_Section> Sections;
	FAnimMontageInstance* AnimMontageInstance = nullptr;
	
	if (UAnimSequence* AnimSequence = Cast<UAnimSequence>(AnimationAsset))
	{
		// FrameRate = AnimSequence->GetSamplingFrameRate().AsDecimal();
		Sections.Add({ 0, AnimSequence->GetMaxCurrentTime(), AnimSequence->GetName()});
	}
	else if (UAnimComposite* AnimComposite = Cast<UAnimComposite>(AnimationAsset))
	{
		// FrameRate = AnimComposite->GetSamplingFrameRate().AsDecimal();
		int32 SegmentNum = AnimComposite->AnimationTrack.AnimSegments.Num();
		for (int32 i = 0; i< SegmentNum; ++i)
		{
			auto& Segment = AnimComposite->AnimationTrack.AnimSegments[i];
			Sections.Add({ Segment.StartPos, Segment.StartPos + Segment.AnimEndTime, Segment.GetAnimReference().GetName()});
			// MaxFrameRate = FMath::Max(MaxFrameRate,Segment.AnimReference->GetSamplingFrameRate().AsDecimal());
		}
		// FrameRate = FMath::Min(MaxFrameRate, FrameRate);
	}
	else if (UAnimMontage* AnimMontage = Cast<UAnimMontage>(AnimationAsset))
	{
		if (USkeletalMeshComponent* SkelMesh = Cast<USkeletalMeshComponent>(PreviewMesh))
		{
			for (auto instance : SkelMesh->GetAnimInstance()->MontageInstances)
			{
				if (instance->Montage == AnimMontage)
				{
					AnimMontageInstance = instance;
					break;
				}
			}
		}
		else return false;

		// double MaxFrameRate = 0;
		TArray<UAnimationAsset*> ReferedSequences;
		AnimMontage->GetAllAnimationSequencesReferred(ReferedSequences);
		for (auto AAsset : ReferedSequences) {
			if (UAnimSequence* Sequence = Cast<UAnimSequence>(AAsset)) {
				// MaxFrameRate = FMath::Max(MaxFrameRate, Sequence->GetSamplingFrameRate().AsDecimal());
			}
		}
		// FrameRate = FMath::Min(AnimMontage->GetSamplingFrameRate().AsDecimal(), MaxFrameRate);

		for (int32 i = 0; i < AnimMontage->GetNumSections(); ++i)
		{
			float SectionStart = 0;
			float SectionEnd = 0;
			FString SectionName = AnimMontage->GetSectionName(i).ToString();
			AnimMontage->GetSectionStartAndEndTime(i, SectionStart, SectionEnd);
			Sections.Add({ SectionStart, SectionEnd, SectionName });
		}
	}
	else return false;

	OutSections.SetNum(Sections.Num());

	//Get ref pos verts
	SetRefPose(PreviewMesh);

	PreviewMesh->RefreshBoneTransforms();
	PreviewMesh->RecreateClothingActors();
	for (int32 P = 0; P < 8; P++) PreviewMesh->GetWorld()->Tick(ELevelTick::LEVELTICK_All, 0.0f);
	PreviewMesh->ClearMotionVector();
	PreviewMesh->RecreateRenderState_Concurrent();
	FlushRenderingCommands();

	TArray<FFinalSkinVertex> RefFinalVerts = static_cast<FSkeletalMeshObjectCPUSkin*>(PreviewMesh->MeshObject)->GetCachedFinalVertices();

	if (RefFinalVerts.IsEmpty()) return false;

	//Calculate frame num for slow task progression
	int32 FrameNum = 0;
	for (auto& Section : Sections)
	{
		double Duration = (Section.End - Section.Start);
		int32 SectionFrameNum = round(Duration * FrameRate);
		FrameNum += SectionFrameNum + 1;
	}
	
	OutVertSize = RefFinalVerts.Num();
	OutVertPos.Reserve(OutVertSize*FrameNum);
	OutVertNormal.Reserve(OutVertSize*FrameNum);

	PreviewMesh->EnablePreview(true, AnimationAsset);
	if (AnimMontageInstance)
	{
		AnimMontageInstance->Pause();
	}
	else PreviewMesh->bPauseAnims = true;

	FScopedSlowTask SlowTask(FrameNum, FText::FromString("Extracting vertex data..."));
	SlowTask.MakeDialog();

	OutFrameRate = FrameRate;
	float FrameTime = 1.0f / FrameRate;
	int32 CurrentStartFrame = 0;

	for (int32 SectionIndex = 0; SectionIndex <Sections.Num(); ++SectionIndex)
	{
		auto& Section = Sections[SectionIndex];
		double Duration = (Section.End - Section.Start);
		int32 SectionFrameNum = round(Duration*FrameRate);
		OutSections[SectionIndex].Name = Section.Name;
		OutSections[SectionIndex].StartFrame = CurrentStartFrame;
		CurrentStartFrame += SectionFrameNum + 1;
		OutSections[SectionIndex].EndFrame = CurrentStartFrame - 1;
		for (int32 j = 0; j <=SectionFrameNum; ++j)
		{
			float CurrentTime = Section.Start + FrameTime * (float)(j) - 0.0001f;
			if (j == 0) CurrentTime = Section.Start;

			if (AnimMontageInstance)
			{
				AnimMontageInstance->SetPosition(CurrentTime);
				PreviewMesh->TickAnimation(0.0f, false);
			}
			else
			{
				PreviewMesh->SetPosition(CurrentTime, false);
			}
			PreviewMesh->RefreshBoneTransforms();
			PreviewMesh->RecreateClothingActors();
			for (int32 P = 0; P < 8; P++) PreviewMesh->GetWorld()->Tick(ELevelTick::LEVELTICK_All, FrameTime);
			PreviewMesh->ClearMotionVector();
			PreviewMesh->RecreateRenderState_Concurrent();
			FlushRenderingCommands();

			TArray<FFinalSkinVertex> FinalVerts = static_cast<FSkeletalMeshObjectCPUSkin*>(PreviewMesh->MeshObject)->GetCachedFinalVertices();
			SlowTask.EnterProgressFrame(1);
			for (int i = 0; i < FinalVerts.Num(); ++i)
			{
				OutVertPos.Add(FVector(FinalVerts[i].Position - RefFinalVerts[i].Position));
				OutVertNormal.Add(FinalVerts[i].TangentZ.ToFVector().GetSafeNormal());
			}
		}
	}

	//Revert settings
	PreviewMesh->SetCPUSkinningEnabled(IsCPUSkinning, true);
	return true;
}

bool FVertexAnimationToolkitModule::ExportPositionTexture(const TArray<FVector>& Verts, int32 Width, int32 MaxWidth, int32 MaxHeight, UTexture2D*& OutTexture)
{
	if (Width == 0 || Verts.Num() / Width == 0 || Width > MaxWidth) return false;

	std::size_t Height = Verts.Num() / Width;

	if (Width > 8192 || Height > 8192)
	{
		UE_LOG(LogTemp, Error, TEXT("%s: Vertex size or frame count exceed 8192!"), *VertexAnimationToolkitTabName.ToString());
	}

	TArray<FFloat16Color> Data;
	Data.SetNum(MaxWidth * MaxHeight);

	FScopedSlowTask SlowTask(Height, FText::FromString("Creating position texture..."));
	SlowTask.MakeDialog();

	for (int i = 0; i < Height; ++i)
	{
		SlowTask.EnterProgressFrame(1);
		for (int j = 0; j < Width; ++j)
		{
			int index = i * Width + j;
			FFloat16Color col;
			col.R = Verts[index].X;
			col.G = Verts[index].Y;
			col.B = Verts[index].Z;
			col.A = 1.0f;
			Data[i * MaxWidth + j] = col;
		}
	}


	UTexture2D* NewTexture = NewObject<UTexture2D>();

	NewTexture->Source.Init(MaxWidth, MaxHeight, 1, 1, TSF_RGBA16F);
	NewTexture->MipGenSettings = TextureMipGenSettings::TMGS_NoMipmaps;
	NewTexture->CompressionSettings = TextureCompressionSettings::TC_HDR;
	NewTexture->SRGB = false;
	NewTexture->Filter = TextureFilter::TF_Nearest;
	NewTexture->LODGroup = TextureGroup::TEXTUREGROUP_UI;

	uint8* TextureData = NewTexture->Source.LockMip(0);

	if (!TextureData) return false;

	const int32 TextureDataSize = NewTexture->Source.CalcMipSize(0);
	FMemory::Memcpy(TextureData, Data.GetData(), TextureDataSize);
	NewTexture->Source.UnlockMip(0);

	NewTexture->UpdateResource();
	NewTexture->PostEditChange();

	OutTexture = NewTexture;
	return true;
}

bool FVertexAnimationToolkitModule::ExportNormalTexture(const TArray<FVector>& Verts, int32 Width, int32 MaxWidth, int32 MaxHeight, UTexture2D*& OutTexture)
{
	if (Width == 0 || Verts.Num() / Width == 0 || Width > MaxWidth) return false;

	std::size_t Height = Verts.Num() / Width;

	if (Width > 8192 || Height > 8192)
	{
		UE_LOG(LogTemp, Error, TEXT("%s: Vertex size or frame count exceed 8192!"), *VertexAnimationToolkitTabName.ToString());
	}

	TArray<FColor> Data;
	Data.SetNumUninitialized(MaxWidth * MaxHeight);
	for (int i = 0; i < Data.Num(); ++i) Data[i] = FColor(0, 0, 0, 255.0);

	FScopedSlowTask SlowTask(Height, FText::FromString("Creating normal texture..."));
	SlowTask.MakeDialog();

	for (int i = 0; i < Height; ++i)
	{
		SlowTask.EnterProgressFrame(1);
		for (int j = 0; j < Width; ++j)
		{
			int index = i * Width + j;
			FColor col;
			col.R = (static_cast<double>(Verts[index].X) + 1.0) / 2.0 * 255.0;
			col.G = (static_cast<double>(Verts[index].Y) + 1.0) / 2.0 * 255.0;
			col.B = (static_cast<double>(Verts[index].Z) + 1.0) / 2.0 * 255.0;
			col.A = 255.0;
			Data[i * MaxWidth + j] = col;
		}
	}


	UTexture2D* NewTexture = NewObject<UTexture2D>();

	NewTexture->Source.Init(MaxWidth, MaxHeight, 1, 1, TSF_BGRA8);
	NewTexture->MipGenSettings = TextureMipGenSettings::TMGS_NoMipmaps;
	NewTexture->CompressionSettings = TextureCompressionSettings::TC_VectorDisplacementmap;
	NewTexture->SRGB = false;
	NewTexture->Filter = TextureFilter::TF_Nearest;
	NewTexture->LODGroup = TextureGroup::TEXTUREGROUP_UI;

	uint8* TextureData = NewTexture->Source.LockMip(0);

	if (!TextureData) return false;

	const int32 TextureDataSize = NewTexture->Source.CalcMipSize(0);
	FMemory::Memcpy(TextureData, Data.GetData(), TextureDataSize);
	NewTexture->Source.UnlockMip(0);

	NewTexture->UpdateResource();
	NewTexture->PostEditChange();

	OutTexture = NewTexture;
	return true;
}

bool FVertexAnimationToolkitModule::MergeTextures(const FString& PackagePath, const FString& Name, TArray<UTexture2D*> Textures)
{
	FString PackageName = PackagePath + Name;
	UPackage* Package = CreatePackage(*PackageName);

	check(Package);
	if (!Package) return false;

	Package->FullyLoad();

	EObjectFlags ObjectFlags = EObjectFlags::RF_Public | EObjectFlags::RF_Standalone;
	UTexture2DArray* TextureArray = NewObject<UTexture2DArray>(Package, *Name, ObjectFlags);

	check(TextureArray);
	if (!TextureArray) return false;

	FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
	AssetRegistryModule.AssetCreated(TextureArray);

	for (auto texture : Textures) {
		TextureArray->SourceTextures.Add(texture);
	}
	TextureArray->UpdateSourceFromSourceTextures();

	for (auto texture : Textures) {
		texture->MarkAsGarbage();
	}

	TextureArray->UpdateResource();
	TextureArray->PostEditChange();
	TextureArray->MarkPackageDirty();

	FVertexAnimationUtils::PushNotification("Successfully baked to Texture", TextureArray);
	return true;
}

bool FVertexAnimationToolkitModule::ExportStaticMesh(const int AdditionalTextureIndexOffset, FString PackagePath, FString Name, UDebugSkelMeshComponent* SkeletalMesh, UStaticMesh*& OutStaticMesh)
{
	SetRefPose(SkeletalMesh);
	UStaticMesh* StaticMesh = FVertexAnimationUtils::ConvertMeshesToStaticMesh(AdditionalTextureIndexOffset, { SkeletalMesh }, FTransform::Identity, PackagePath+Name);

	if (!StaticMesh)
	{
		UE_LOG(LogTemp, Warning, TEXT("Failed to convert skeletal mesh to static mesh"));
		return false;
	}
	OutStaticMesh = StaticMesh;
	return true;
}

void FVertexAnimationToolkitModule::SetForcedLOD(UDebugSkelMeshComponent* PreviewMesh, int32 lod)
{
	if (USkinnedMeshComponent* MasterPoseComponentPtr = PreviewMesh->MasterPoseComponent.Get())
	{
		MasterPoseComponentPtr->SetForcedLOD(lod);
		MasterPoseComponentPtr->UpdateLODStatus();
		MasterPoseComponentPtr->RefreshBoneTransforms(nullptr);
	}
	else {
		PreviewMesh->SetForcedLOD(lod);
		PreviewMesh->UpdateLODStatus();
		PreviewMesh->RefreshBoneTransforms(nullptr);
	}
}

void FVertexAnimationToolkitModule::SetRefPose(UDebugSkelMeshComponent* PreviewMesh)
{
	PreviewMesh->EnablePreview(true, NULL);
	PreviewMesh->RefreshBoneTransforms(nullptr);
	PreviewMesh->ClearMotionVector();
	PreviewMesh->RecreateRenderState_Concurrent();
	FlushRenderingCommands();
}

bool FVertexAnimationToolkitModule::ExportPlayHelper(FString PackagePath, FString Name, TArray<FAnimationSection>& Sections, float FrameRate)
{
	int32 MaxFrames = Sections.Last().EndFrame+1;

	UPackage* Package = CreatePackage(*(PackagePath+Name));
	Package->FullyLoad();

	UMaterialFunction* MaterialFunction = NewObject<UMaterialFunction>(Package, *Name, RF_Public | RF_Standalone);

	UMaterialExpressionFunctionInput* AnimationToPlay = NewObject<UMaterialExpressionFunctionInput>(MaterialFunction);
	AnimationToPlay->InputName = TEXT("Animation To Play");
	AnimationToPlay->Description = "Select animation at index 0-" + FString::FromInt(Sections.Num());
	AnimationToPlay->MaterialExpressionGuid = FGuid::NewGuid();
	AnimationToPlay->InputType = EFunctionInputType::FunctionInput_Scalar;
	AnimationToPlay->bUsePreviewValueAsDefault = true;
	AnimationToPlay->SortPriority = 0;

	// use FindObject() for unexposed functions 

	MaterialFunction->GetExpressionCollection().AddExpression(AnimationToPlay);

	UMaterialExpressionFunctionInput* TransitionFrames = NewObject<UMaterialExpressionFunctionInput>(MaterialFunction);
	TransitionFrames->InputName = TEXT("Transition Frames");
	TransitionFrames->Description = TEXT("Include frames from previous animation for smooth transition");
	TransitionFrames->MaterialExpressionGuid = FGuid::NewGuid();
	TransitionFrames->SortPriority = 1;
	TransitionFrames->bUsePreviewValueAsDefault = true;
	TransitionFrames->InputType = EFunctionInputType::FunctionInput_Scalar;
	MaterialFunction->GetExpressionCollection().AddExpression(TransitionFrames);

	TArray<UMaterialExpressionIf*> NestedIfs;

	for (int32 i = 0; i < Sections.Num(); ++i) {
		UMaterialExpressionConstant3Vector* Expr = NewObject<UMaterialExpressionConstant3Vector>(MaterialFunction);
		Expr->Constant.R = Sections[i].StartFrame;
		Expr->Constant.G = Sections[i].EndFrame;
		Expr->MaterialExpressionGuid = FGuid::NewGuid();
		MaterialFunction->GetExpressionCollection().AddExpression(Expr);

		UMaterialExpressionConstant* Constant = NewObject<UMaterialExpressionConstant>(MaterialFunction);
		Constant->R = i;
		Constant->MaterialExpressionGuid = FGuid::NewGuid();
		MaterialFunction->GetExpressionCollection().AddExpression(Constant);

		UMaterialExpressionIf* IfExpr = NewObject<UMaterialExpressionIf>(MaterialFunction);
		IfExpr->A.Expression = AnimationToPlay;
		IfExpr->B.Expression = Constant;
		IfExpr->AEqualsB.Expression = Expr;
		IfExpr->MaterialExpressionGuid = FGuid::NewGuid();
		MaterialFunction->GetExpressionCollection().AddExpression(IfExpr);
		NestedIfs.Add(IfExpr);
	}

	UMaterialExpressionConstant3Vector* ConstantVectorZero = NewObject<UMaterialExpressionConstant3Vector>(MaterialFunction);
	ConstantVectorZero->Constant.R = 0;
	ConstantVectorZero->Constant.G = 0;
	ConstantVectorZero->Constant.B = 0;
	ConstantVectorZero->MaterialExpressionGuid = FGuid::NewGuid();
	MaterialFunction->GetExpressionCollection().AddExpression(ConstantVectorZero);

	NestedIfs.Last()->AGreaterThanB.Expression = ConstantVectorZero;
	NestedIfs.Last()->ALessThanB.Expression = ConstantVectorZero;

	for (int32 i = 0; i < NestedIfs.Num()-1; ++i) {
		NestedIfs[i]->AGreaterThanB.Expression = NestedIfs[i + 1];
		NestedIfs[i]->ALessThanB.Expression = NestedIfs[i + 1];
	}

	UMaterialExpressionClamp* StartFrameClamped = NewObject< UMaterialExpressionClamp>(MaterialFunction);

	{
		UMaterialExpressionComponentMask* StartFrameMask = NewObject< UMaterialExpressionComponentMask>(MaterialFunction);
		StartFrameMask->R = 1;
		StartFrameMask->G = 0;
		StartFrameMask->B = 0;
		StartFrameMask->A = 0;
		StartFrameMask->Input.Expression = NestedIfs[0];
		StartFrameMask->MaterialExpressionGuid = FGuid::NewGuid();
		MaterialFunction->GetExpressionCollection().AddExpression(StartFrameMask);

		//Subtract if transitional frame
		UMaterialExpressionSubtract* StartFrameSubtract = NewObject< UMaterialExpressionSubtract>(MaterialFunction);
		StartFrameSubtract->A.Expression = StartFrameMask;
		StartFrameSubtract->B.Expression = TransitionFrames;
		StartFrameSubtract->MaterialExpressionGuid = FGuid::NewGuid();
		MaterialFunction->GetExpressionCollection().AddExpression(StartFrameSubtract);

		StartFrameClamped->Input.Expression = StartFrameSubtract;
		StartFrameClamped->MinDefault = 0.0f;
		StartFrameClamped->Max.Expression = StartFrameMask;
		StartFrameClamped->MaterialExpressionGuid = FGuid::NewGuid();
		MaterialFunction->GetExpressionCollection().AddExpression(StartFrameClamped);
	}

	UMaterialExpressionComponentMask* EndFrameMask = NewObject< UMaterialExpressionComponentMask>(MaterialFunction);
	EndFrameMask->R = 0;
	EndFrameMask->G = 1;
	EndFrameMask->B = 0;
	EndFrameMask->A = 0;
	EndFrameMask->Input.Expression = NestedIfs[0];
	EndFrameMask->MaterialExpressionGuid = FGuid::NewGuid();
	MaterialFunction->GetExpressionCollection().AddExpression(EndFrameMask);

	UMaterialExpressionConstant* InMaxFrame = NewObject< UMaterialExpressionConstant>(MaterialFunction);
	InMaxFrame->R = MaxFrames;
	InMaxFrame->MaterialExpressionGuid = FGuid::NewGuid();
	MaterialFunction->GetExpressionCollection().AddExpression(InMaxFrame);

	// Create and configure a function output node
	UMaterialExpressionFunctionOutput* OutStartFrame = NewObject<UMaterialExpressionFunctionOutput>(MaterialFunction);
	OutStartFrame->OutputName = TEXT("Start Frame");
	OutStartFrame->SortPriority = 0;
	OutStartFrame->A.Expression = StartFrameClamped;
	OutStartFrame->MaterialExpressionGuid = FGuid::NewGuid();
	MaterialFunction->GetExpressionCollection().AddExpression(OutStartFrame);

	UMaterialExpressionFunctionOutput* OutEndFrame = NewObject<UMaterialExpressionFunctionOutput>(MaterialFunction);
	OutEndFrame->OutputName = TEXT("End Frame");
	OutEndFrame->SortPriority = 1;
	OutEndFrame->A.Expression = EndFrameMask;
	OutEndFrame->MaterialExpressionGuid = FGuid::NewGuid();
	MaterialFunction->GetExpressionCollection().AddExpression(OutEndFrame);

	UMaterialExpressionFunctionOutput* OutMaxFrames = NewObject<UMaterialExpressionFunctionOutput>(MaterialFunction);
	OutMaxFrames->OutputName = TEXT("Max Frames");
	OutMaxFrames->SortPriority = 2;
	OutMaxFrames->A.Expression = InMaxFrame;
	OutMaxFrames->MaterialExpressionGuid = FGuid::NewGuid();
	MaterialFunction->GetExpressionCollection().AddExpression(OutMaxFrames);

	//APPLY FROM EDITOR TO FIX BUG
	auto AssetEditor = GEditor->GetEditorSubsystem<UAssetEditorSubsystem>();
	if (AssetEditor->OpenEditorForAsset(MaterialFunction, EToolkitMode::Standalone, nullptr, false))
	{
		auto Editor = AssetEditor->FindEditorForAsset(MaterialFunction, false);
		if (FMaterialEditor* MaterialEditor = static_cast<FMaterialEditor*>(Editor))
		{
			MaterialEditor->UpdateOriginalMaterial();
		}
		Editor->CloseWindow();
	}

	// Save the package
	FAssetRegistryModule::AssetCreated(MaterialFunction);
	MaterialFunction->PostEditChange();
	MaterialFunction->MarkPackageDirty();

	FVertexAnimationUtils::PushNotification("Successfully generated helper function", MaterialFunction);

	
	return true;
}

TOptional<FVertexAnimationToolkitModule::FConfiguration> FVertexAnimationToolkitModule::ShowConfigurationModal()
{
	FConfiguration Config = {};
	bool bAccepted = false;

	TSharedRef<SWindow> PopupWindow = SNew(SWindow)
		.Title(FText::FromString("Vertex Animation Toolkit"))
		.SizingRule(ESizingRule::Autosized)
		.SupportsMinimize(false)
		.SupportsMaximize(false)
		.HasCloseButton(true);

	auto MakeRadioButton = [&](const FString& Label, int32 Index, TSharedPtr<int32> SelectedIndexPtr)
		{
			return SNew(SCheckBox)
				.Style(FCoreStyle::Get(), "RadioButton") // Use radio style
				.IsChecked_Lambda([SelectedIndexPtr, Index]() {
				return *SelectedIndexPtr == Index ? ECheckBoxState::Checked : ECheckBoxState::Unchecked;
					})
				.OnCheckStateChanged_Lambda([SelectedIndexPtr, Index](ECheckBoxState NewState) {
				if (NewState == ECheckBoxState::Checked)
				{
					*SelectedIndexPtr = Index;
				}
					})
				[
					SNew(STextBlock).Text(FText::FromString(Label))
				];
		};

	TSharedPtr<int32> BoneOrVertexRadio = MakeShared<int32>(0);

	PopupWindow->SetContent(
		SNew(SVerticalBox)
		+ SVerticalBox::Slot().AutoHeight().Padding(10, 10, 0, 6)
		[
			MakeRadioButton(TEXT("Bone Animations"), 0, BoneOrVertexRadio)
		]
		+ SVerticalBox::Slot().AutoHeight().Padding(10, 0)
		[
			MakeRadioButton(TEXT("Vertex Animations"), 1, BoneOrVertexRadio)
		]
		+ SVerticalBox::Slot().AutoHeight().Padding(0, 10, 10, 7).HAlign(HAlign_Right)
		[
			SNew(SHorizontalBox)
				+ SHorizontalBox::Slot().AutoWidth().HAlign(HAlign_Center)
				[
					SNew(SButton)
						.Text(FText::FromString("OK"))
						.OnClicked_Lambda([&Config, &bAccepted, &BoneOrVertexRadio, PopupWindow]()
							{
								Config.bBoneAnimations = *BoneOrVertexRadio == 0;
								bAccepted = true;
								PopupWindow->RequestDestroyWindow();
								return FReply::Handled();
							})
				]
		]
		);

	// This blocks until window closes
	FSlateApplication::Get().AddModalWindow(PopupWindow, FSlateApplication::Get().GetActiveTopLevelWindow());

	// When execution resumes, return the result
	if (bAccepted)
	{
		return Config;
	}

	return TOptional<FVertexAnimationToolkitModule::FConfiguration>(); // Empty if cancelled
}

UObject* FVertexAnimationToolkitModule::LoadPluginContent(const FString& Path)
{
	FString FullPath = "/" + FString(VATK_PluginName) + "/" + Path;
	UObject* Object = LoadObject<UObject>(nullptr, *FullPath);
	if (!Object)
	{
		UE_LOG(LogTemp, Warning, TEXT("Object not found at path: %s"), *FullPath);
	}
	return Object;
}

#undef LOCTEXT_NAMESPACE
	
IMPLEMENT_MODULE(FVertexAnimationToolkitModule, VertexAnimationToolkit)