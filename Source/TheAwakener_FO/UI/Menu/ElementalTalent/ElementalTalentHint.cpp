// Fill out your copyright notice in the Description page of Project Settings.


#include "ElementalTalentHint.h"
#include "Engine/Texture2D.h"
#include "TheAwakener_FO/FunctionLibrary/ResourceFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void UElementalTalentHint::NativeOnInitialized()
{
	Super::NativeOnInitialized();
	this->TalentIcon = Cast<UImage>(GetWidgetFromName(TEXT("Icon")));
	this->TalentName = Cast<UTextBlock>(GetWidgetFromName(TEXT("NameText")));
	this->DescText = Cast<UTextBlock>(GetWidgetFromName(TEXT("Desc")));
	this->OccurTypeIcon = Cast<UImage>(GetWidgetFromName(TEXT("TypeIcon")));
	this->OccurTypeDesc = Cast<UTextBlock>(GetWidgetFromName(TEXT("TypeText")));
}

void UElementalTalentHint::Set(FElementalTalentUIInfo ToShow, bool HasLearnt)
{
	this->UIInfo = ToShow;
	this->Learnt = HasLearnt;
	
	Draw();
	this->SetVisibility(ESlateVisibility::Visible);
}

void UElementalTalentHint::Draw()
{
	if (this->TalentIcon)
	{
		UTexture2D* IconTexture = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(this->UIInfo.IconPath));
		if (IconTexture)
		{
			TalentIcon->SetVisibility(ESlateVisibility::Visible);
			TalentIcon->SetBrushFromTexture(IconTexture);
		}else
		{
			TalentIcon->SetVisibility(ESlateVisibility::Hidden);
		}
	}
	if (TalentName)
	{
		this->TalentName->SetText(FText::FromString(UIInfo.Name));
	}
	if (OccurTypeIcon)
	{
		FString APath = FString();
		switch (UIInfo.ElementalTrigger)
		{
		case EElementalTriggerType::Action: APath = "ArtResource/UI/UI/Elemental/Icon/occur_onaction"; break;
		case EElementalTriggerType::Passive: APath = "ArtResource/UI/UI/Elemental/Icon/occur_passive"; break;
		case EElementalTriggerType::OnHit: APath = "ArtResource/UI/UI/Elemental/Icon/occur_onhit"; break;
		case EElementalTriggerType::OnUse: APath = "ArtResource/UI/UI/Elemental/Icon/occur_onuse"; break;
		}
		if (APath.IsEmpty() == false)
		{
			UTexture2D* IconTexture = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath(APath));
			if (IconTexture)
			{
				OccurTypeIcon->SetVisibility(ESlateVisibility::Visible);
				OccurTypeIcon->SetBrushFromTexture(IconTexture);
			}else
			{
				OccurTypeIcon->SetVisibility(ESlateVisibility::Hidden);
			}
		}
	}
	if (OccurTypeDesc)
	{
		FString ShowText = FString();
		switch (UIInfo.ElementalTrigger)
		{
		case EElementalTriggerType::Action: ShowText = UGameplayFuncLib::GetAwDataManager()->GetTextByKey("Elemental_Occur_Action"); break;
		case EElementalTriggerType::Passive: ShowText = UGameplayFuncLib::GetAwDataManager()->GetTextByKey("Elemental_Occur_Passive"); break;
		case EElementalTriggerType::OnHit: ShowText = UGameplayFuncLib::GetAwDataManager()->GetTextByKey("Elemental_Occur_OnHit"); break;
		case EElementalTriggerType::OnUse: ShowText = UGameplayFuncLib::GetAwDataManager()->GetTextByKey("Elemental_Occur_OnUse"); break;
		}
		OccurTypeDesc->SetText(FText::FromString(ShowText));
	}
	if (DescText)
	{
		FString ShowText = FString();
		if (Learnt == false)
		{
			ShowText.Append(UGameplayFuncLib::GetAwDataManager()->GetTextByKey("RequireReachLevel"));
				ShowText.Append(FString::FromInt(UIInfo.Level));
				ShowText.Append("\n");
		}
		ShowText.Append(UIInfo.BaseEffect);
		for (const TTuple<ETerrainType, FString> TEffect : UIInfo.TerrainEffect)
		{
			ShowText.Append("\n");
			ShowText.Append(UGameplayFuncLib::GetAwDataManager()->GetTerrainText(TEffect.Key)).Append(":").Append(TEffect.Value);
		}
		for (const TTuple<EWeatherType, FString> WEffect : UIInfo.WeatherEffect)
		{
			ShowText.Append("\n");
			ShowText.Append(UGameplayFuncLib::GetAwDataManager()->GetWeatherText(WEffect.Key)).Append(":").Append(WEffect.Value);
		}
		DescText->SetText(FText::FromString(ShowText));
	}
}