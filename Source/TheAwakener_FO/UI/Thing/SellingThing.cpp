// Fill out your copyright notice in the Description page of Project Settings.


#include "SellingThing.h"
#include "Engine/Texture2D.h"
#include "TheAwakener_FO/UI/Shop.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/ResourceFuncLib.h"
#include "TheAwakener_FO/GamePlay/Thing/ThingUIInfo.h"


void USellingThing::NativeOnInitialized()
{
	Super::NativeOnInitialized();
	ItemIcon = Cast<UImage>(GetWidgetFromName(TEXT("ItemIconView")));
	PriceList = Cast<UPriceView>(GetWidgetFromName(TEXT("WBP_PriceView")));
	CantAffortMask = Cast<UCanvasPanel>(GetWidgetFromName(TEXT("CantAffort")));
	ThisButton = Cast<UButton>(GetWidgetFromName(TEXT("BackButton")));
	SelectSign = Cast<UImage>(GetWidgetFromName(TEXT("SelectMask")));
	if (ThisButton) ThisButton->OnClicked.AddDynamic(this, &USellingThing::OnSelected);
}

void USellingThing::NativeOnListItemObjectSet(UObject* ListItemObject)
{
	// IUserObjectListEntry::NativeOnListItemObjectSet(ListItemObject);

	const USellingThing* SellingThing = Cast<USellingThing>(ListItemObject);
	this->Set(SellingThing->Shop, SellingThing->Deal);

	if (ItemIcon && this->Deal.Icon.IsEmpty() == false)
	{
		UTexture2D* IconTexture = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath( this->Deal.Icon));
		if (IconTexture)
		{
			ItemIcon->SetBrushFromTexture(IconTexture);
			ItemIcon->SetVisibility(ESlateVisibility::Visible);
		}else
		{
			ItemIcon->SetVisibility(ESlateVisibility::Hidden);
		}
	}
	if (PriceList && this->Deal.Price.Num())
	{
		for (int i = 0; i < Deal.Price.Num(); i++)
		{
			//TODO 现在就只能显示货币，得改进这个CurrencyIcon要不就是别的方法
			if (Deal.Price[i].Type != EThingType::Currency ) continue;
			
			if (this->Shop->Trading.CurrencyIconPath["Gold"].IsEmpty() == false)
				PriceList->Set(this->Shop->Trading.CurrencyIconPath["Gold"], Deal.Price[i].Count);
			break;
		}
	}

	if (CantAffortMask && this->Shop )
	{
		const bool CanAffort = this->Shop->Enough(this->Deal);
		CantAffortMask->SetVisibility(
			CanAffort == true ?
			ESlateVisibility::Hidden : ESlateVisibility::Visible
		);
	}

	if (ThisButton && SellingThing && SellingThing->ThisButton)
	{
		ThisButton->OnClicked = SellingThing->ThisButton->OnClicked;
	}

	if (SelectSign)
	{
		this->SelectSign->SetVisibility(ESlateVisibility::Hidden);
	}
}

/**
 * @param ThisDeal 这笔交易的数据
 * @param CurrencyShowIcon 这笔交易中货币的Icon
 */
void USellingThing::Set(UShop* FromShop, FDeal ThisDeal)
{
	this->Deal = ThisDeal;
	this->Shop = FromShop;
}

void USellingThing::NativeOnItemSelectionChanged(bool bIsSelected)
{
	if (bIsSelected)
	{
		SelectSign->SetVisibility(ESlateVisibility::HitTestInvisible);
	}
	else
	{
		SelectSign->SetVisibility(ESlateVisibility::Hidden);
	}
}

void USellingThing::OnSelected()
{
	if (this->Shop)
		this->Shop->ItemSelected(this->Deal);
}