// Fill out your copyright notice in the Description page of Project Settings.


#include "CharacterHitBoxDeactive.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UCharacterHitBoxDeactive::DeactiveHitBoxes(USkeletalMeshComponent* MeshComp)
{
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	if (this->AllCharacterHitBoxes == true)
	{
		// UKismetSystemLibrary::PrintString(this, FString("Start Deactivate All Hit Box.")
		// 	.Append(FString::SanitizeFloat(Me->Lived)), true, true, FLinearColor::Green, 20);
		Me->SetAllCharacterHitBoxActive(false);
	}else
	{
		for (const FString BoxName : this->CharacterHitBoxName)
		{
			auto Box = Me->GetCharacterHitBoxByName(BoxName);
			if (Box.Get<1>() && Box.Get<1>()->Type == ECharacterHitBoxType::Normal)
			{
				Box.Get<1>()->Active = false;
				Box.Get<0>()->SetActive(Box.Get<1>()->Active);
			}
		}
	}
}

void UCharacterHitBoxDeactive::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
                                           const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyBegin(MeshComp, Animation, TotalDuration, EventReference);

	DeactiveHitBoxes(MeshComp);
}

void UCharacterHitBoxDeactive::NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	float FrameDeltaTime, const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyTick(MeshComp, Animation, FrameDeltaTime, EventReference);

	DeactiveHitBoxes(MeshComp);
}

void UCharacterHitBoxDeactive::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
                                         const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyEnd(MeshComp, Animation, EventReference);
	
	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	const UAnimMontage* Mont = Cast<UAnimMontage>(Animation);
	const FAnimMontageInstance* MontageInstance = Mont ? Me->GetAwAnimInstance()->GetActiveInstanceForMontage(Mont) : nullptr;
	const bool Playing = MontageInstance ? MontageInstance->IsPlaying() : false;

	if (Mont && MontageInstance && Playing == false) return;
	
	if (this->AllCharacterHitBoxes == true)
	{
		// UKismetSystemLibrary::PrintString(this, FString("End Deactivate All Hit Box.")
		// 	.Append(FString::SanitizeFloat(Me->Lived)), true, true, FLinearColor::Yellow, 20);
		Me->ResetAllCharacterHitBoxes();
	}else
	{
		for (const FString BoxName : this->CharacterHitBoxName)
		{
			auto Box = Me->GetCharacterHitBoxByName(BoxName);
			if (Box.Get<1>() && Box.Get<1>()->Type == ECharacterHitBoxType::Normal)
			{
				Box.Get<1>()->Active = Box.Get<1>()->DefaultActive;
				Box.Get<0>()->SetActive(Box.Get<1>()->Active);
			}
		}
	}
}
