// Fill out your copyright notice in the Description page of Project Settings.


#include "MontageDead.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UMontageDead::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);

	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	UAnimMontage* Montage = Me->GetCurrentActiveMontage();
	if (!Montage) return;
	Montage->RateScale = 0;
}
