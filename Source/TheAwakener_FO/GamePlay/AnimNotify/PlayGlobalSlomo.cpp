// Fill out your copyright notice in the Description page of Project Settings.


#include "PlayGlobalSlomo.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GameFramework/Base/AwGameModeBase.h"


void UPlayGlobalSlomo::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	PRAGMA_DISABLE_DEPRECATION_WARNINGS
	Super::Notify(MeshComp, Animation);
	PRAGMA_ENABLE_DEPRECATION_WARNINGS
	AAwCharacter* Character = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!IsValid(Character)) return;
	UTimelineManager* TimeManager = UGameplayFuncLib::GetTimelineManager();
	if (TimeManager)
	{
		FTimeSlomoData SlomoData = FTimeSlomoData();
		SlomoData.Duration = Duration;
		SlomoData.Rate =SlowDownRate;
		SlomoData.BackRate = NormalRate;
		SlomoData.Priority = Priority;
		TimeManager->SetGlobalTimeSlomo(SlomoData);
	}
	
}
