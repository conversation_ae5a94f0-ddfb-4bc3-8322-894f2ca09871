// Fill out your copyright notice in the Description page of Project Settings.


#include "SettingsUI.h"

#include "Blueprint/WidgetBlueprintLibrary.h"
#include "Engine/Texture2D.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"

void USettingsUI::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	SettingsName_TextBlock = Cast<UTextBlock>(GetWidgetFromName("SettingsName"));
	DragBarUI = Cast<UDragBarUI>(GetWidgetFromName("WBP_DragBar"));
	SwitchoverUI = Cast<USwitchoverUI>(GetWidgetFromName("WBP_Switchover"));
	SelectImage = Cast<UImage>(GetWidgetFromName("Select"));
	SelectImage1 = Cast<UImage>(GetWidgetFromName("Select_1"));

	if(IsValid(DragBarUI))
		DragBarUI->SetVisibility(ESlateVisibility::Collapsed);
	if(IsValid(SwitchoverUI))
		SwitchoverUI->SetVisibility(ESlateVisibility::Collapsed);
	if(IsValid(SelectImage))
		SelectImage->SetVisibility(ESlateVisibility::Collapsed);
	if(IsValid(SelectImage1))
		SelectImage1->SetVisibility(ESlateVisibility::Collapsed);
}

void USettingsUI::NativeConstruct()
{
	Super::NativeConstruct();

	if(IsDragBar)
	{
		if(DragBarUI)
		{
			if(Tag == "MainVolume")
			{
				DragBarUI->SetDragBarValue(UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetMainVolume());
				UGameplayFuncLib::GetAwGameInstance()->SetMainSoundVolume(DragBarUI->GetDragBarValue());
			}
			else if(Tag == "Music")
			{
				DragBarUI->SetDragBarValue(UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetBGMVolume());
				UGameplayFuncLib::GetAwGameInstance()->SetBGMVolume(DragBarUI->GetDragBarValue());
			}
			else if(Tag == "SoundEffect")
			{
				DragBarUI->SetDragBarValue(UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetSFXVolume());
				UGameplayFuncLib::GetAwGameInstance()->SetSFXVolume(DragBarUI->GetDragBarValue());
			}
			else if(Tag == "Voice")
			{
				DragBarUI->SetDragBarValue(UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetVoiceVolume());
				UGameplayFuncLib::GetAwGameInstance()->SetVoiceVolume(DragBarUI->GetDragBarValue());
			}
			else if(Tag == "UISound")
			{
				DragBarUI->SetDragBarValue(UGameplayFuncLib::GetAwGameInstance()->RoleInfo.GetUISoundVolume());
				UGameplayFuncLib::GetAwGameInstance()->SetUISoundVolume(DragBarUI->GetDragBarValue());
			}
			DragBarUI->SetVisibility(ESlateVisibility::Visible);
		}
			
	}
	else
	{
		if(SwitchoverUI)
		{
			/*if(Tag == "Language")
				SwitchoverUI->SetTag(Tag);
			else if(Tag == "KeyPromptType")
				SwitchoverUI->SetTag(Tag);
			else if(Tag == "CameraAimAssist")
				SwitchoverUI->SetTag(Tag);*/
			
			SwitchoverUI->SetTag(Tag);
			
			SwitchoverUI->RefreshUI();
			SwitchoverUI->SetVisibility(ESlateVisibility::Visible);
		}
	}
	
	RefreshUI();
}

void USettingsUI::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
	Super::NativeTick(MyGeometry, InDeltaTime);

	if(IsDragBar)
	{
		if(DragBarUI && DragBarUI->GetButtonIsDown())
		{
			if(Tag == "MainVolume")
			{
				UGameplayFuncLib::GetAwGameInstance()->RoleInfo.SaveMainVolume(DragBarUI->GetDragBarValue());
				UGameplayFuncLib::GetAwGameInstance()->SetMainSoundVolume(DragBarUI->GetDragBarValue());
			}
			else if(Tag == "Music")
			{
				UGameplayFuncLib::GetAwGameInstance()->RoleInfo.SaveBGMVolume(DragBarUI->GetDragBarValue());
				UGameplayFuncLib::GetAwGameInstance()->SetBGMVolume(DragBarUI->GetDragBarValue());
			}
			else if(Tag == "SoundEffect")
			{
				UGameplayFuncLib::GetAwGameInstance()->RoleInfo.SaveSFXVolume(DragBarUI->GetDragBarValue());
				UGameplayFuncLib::GetAwGameInstance()->SetSFXVolume(DragBarUI->GetDragBarValue());
			}
			else if(Tag == "Voice")
			{
				UGameplayFuncLib::GetAwGameInstance()->RoleInfo.SaveVoiceVolume(DragBarUI->GetDragBarValue());
				UGameplayFuncLib::GetAwGameInstance()->SetVoiceVolume(DragBarUI->GetDragBarValue());
			}
			else if(Tag == "UISound")
			{
				UGameplayFuncLib::GetAwGameInstance()->RoleInfo.SaveUISoundVolume(DragBarUI->GetDragBarValue());
				UGameplayFuncLib::GetAwGameInstance()->SetUISoundVolume(DragBarUI->GetDragBarValue());
			}
		}
	}
}

void USettingsUI::NativeOnMouseEnter(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
	Super::NativeOnMouseEnter(InGeometry, InMouseEvent);
	CurFocusChange();
}

void USettingsUI::NativeOnMouseLeave(const FPointerEvent& InMouseEvent)
{
	Super::NativeOnMouseLeave(InMouseEvent);
	LastFocusChange();
}

void USettingsUI::CurFocusChange()
{
	ChangeFocusIndex.Broadcast(Index);
	CurFocusChangeUpdateView();
}

void USettingsUI::CurFocusChangeUpdateView()
{
	SetIsFocus(true);
	SelectImage->SetVisibility(ESlateVisibility::Visible);
	SelectImage1->SetVisibility(ESlateVisibility::Visible);
	if(IsDragBar)
	{
		DragBarUI->Drag_BT->SetVisibility(ESlateVisibility::Visible);
		UTexture2D* SelectProgress = LoadObject<UTexture2D>(nullptr,
			*UResourceFuncLib::GetAssetPath(UGameplayFuncLib::GetAwDataManager()->GetItemIconById("Select_Slot").Path));
		if(SelectProgress)
		{
			const FSlateBrush SelectProgress_Brush = UWidgetBlueprintLibrary::MakeBrushFromTexture(
				SelectProgress,SelectProgress->GetSizeX(),SelectProgress->GetSizeY());
			//DragBarUI->Drag_ProgressBar->WidgetStyle.SetFillImage(SelectProgress_Brush);
			FProgressBarStyle TempStyle = DragBarUI->Drag_ProgressBar->GetWidgetStyle();
			TempStyle.SetFillImage(SelectProgress_Brush);
			DragBarUI->Drag_ProgressBar->SetWidgetStyle(TempStyle);

		}
	}
	else
	{
		SwitchoverUI->Left_BT->SetVisibility(ESlateVisibility::Visible);
		SwitchoverUI->Right_BT->SetVisibility(ESlateVisibility::Visible);
	}
}

void USettingsUI::LastFocusChange()
{
	SetIsFocus(false);
	SelectImage->SetVisibility(ESlateVisibility::Collapsed);
	SelectImage1->SetVisibility(ESlateVisibility::Collapsed);
	if(IsDragBar)
	{
		DragBarUI->Drag_BT->SetVisibility(ESlateVisibility::Collapsed);
		
		UTexture2D* NotSelectProgress = LoadObject<UTexture2D>(nullptr,
			*UResourceFuncLib::GetAssetPath(UGameplayFuncLib::GetAwDataManager()->GetItemIconById("NotSelect_Slot").Path));
		if(NotSelectProgress)
		{
			const FSlateBrush NotSelectProgress_Brush = UWidgetBlueprintLibrary::MakeBrushFromTexture(
				NotSelectProgress,NotSelectProgress->GetSizeX(),NotSelectProgress->GetSizeY());
			//DragBarUI->Drag_ProgressBar->WidgetStyle.SetFillImage(NotSelectProgress_Brush);
			FProgressBarStyle TempStyle = DragBarUI->Drag_ProgressBar->GetWidgetStyle();
			TempStyle.SetFillImage(NotSelectProgress_Brush);
			DragBarUI->Drag_ProgressBar->SetWidgetStyle(TempStyle);
		}
	}
	else
	{
		SwitchoverUI->Left_BT->SetVisibility(ESlateVisibility::Collapsed);
		SwitchoverUI->Right_BT->SetVisibility(ESlateVisibility::Collapsed);
	}
}


void USettingsUI::UpdateView()
{
	
}

void USettingsUI::RefreshUI()
{
	if(SettingsName_TextBlock)
		SettingsName_TextBlock->SetText(FText::FromString(UGameplayFuncLib::GetAwDataManager()->GetTextByKey(Tag)));
	
	if (SwitchoverUI)
		SwitchoverUI->RefreshUI();
}
