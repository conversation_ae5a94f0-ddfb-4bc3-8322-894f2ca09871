// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState_TimedParticleEffect.h"
#include "Engine/DataTable.h"
#include "Particles/ParticleSystemComponent.h"
#include "CheckBuffAnimNotifyState_PlayParticleEffect.generated.h"


USTRUCT()
struct FAnimStatePlayParticleEffectData  :public  FTableRowBase
{
	GENERATED_BODY()
	// The particle system template to use when spawning the particle component
	UPROPERTY(EditAnywhere, Category = ParticleSystem, meta = (ToolTip = "The particle system to spawn for the notify state"))
	TObjectPtr<UParticleSystem> PSTemplate;

	// The socket within our mesh component to attach to when we spawn the particle component
	UPROPERTY(EditAnywhere, Category = ParticleSystem, meta = (ToolTip = "The socket or bone to attach the system to"))
	FName SocketName;

	// Offset from the socket / bone location
	UPROPERTY(EditAnywhere, Category = ParticleSystem, meta = (ToolTip = "Offset from the socket or bone to place the particle system"))
	FVector LocationOffset;

	// Offset from the socket / bone rotation
	UPROPERTY(EditAnywhere, Category = ParticleSystem, meta = (ToolTip = "Rotation offset from the socket or bone for the particle system"))
	FRotator RotationOffset;

	// Whether or not we destroy the component at the end of the notify or instead just stop
	// the emitters.
	UPROPERTY(EditAnywhere, Category = ParticleSystem, meta = (DisplayName = "Destroy Immediately", ToolTip = "Whether the particle system should be immediately destroyed at the end of the notify state or be allowed to finish"))
	bool bDestroyAtEnd;
};
/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UCheckBuffAnimNotifyState_PlayParticleEffect : public UAnimNotifyState_TimedParticleEffect
{
	GENERATED_BODY()
public:
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<FString> AbilityIdCheck; 
	
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		TArray<FString> CheckBuffTags;
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
		UDataTable* ParticleParameterData = nullptr;

	TSet<TWeakObjectPtr<UParticleSystemComponent>> FXChildren;
	
	virtual void NotifyBegin(USkeletalMeshComponent * MeshComp, UAnimSequenceBase * Animation, float TotalDuration, const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyEnd(USkeletalMeshComponent * MeshComp, UAnimSequenceBase * Animation, const FAnimNotifyEventReference& EventReference) override;

	void DestroyParticleOnEnd(USkeletalMeshComponent * MeshComp, UAnimSequenceBase * Animation, const FAnimNotifyEventReference& EventReference);
};
