// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "Dom/JsonObject.h"
#include "CoreMinimal.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "TableAddBuffInfo.generated.h"

/**
 * 从其他数据表（比如职业表）读取的添加Buff信息
 */
USTRUCT(BlueprintType)
struct FTableAddBuffInfo
{
	GENERATED_BODY()
public:
	//要添加的BuffId，如果Buff表里没有，就添加不了了
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString Id;

	//要添加的层数
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	int Stack = 0;

	//要设定的时间，秒
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	float Time = 0;

	//是否永久
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	bool Infinity = false;

	FTableAddBuffInfo(){}
	FTableAddBuffInfo(FString BuffId, int AddStack, float SetTime, bool InfinityTime):
		Id(BuffId), Stack(AddStack), Time(SetTime), Infinity(InfinityTime){}

	static FTableAddBuffInfo FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		return FTableAddBuffInfo(
			UDataFuncLib::AwGetStringField(JsonObj, "Id", ""),
			UDataFuncLib::AwGetNumberField(JsonObj, "Stack", 0),
			UDataFuncLib::AwGetNumberField(JsonObj, "Time", 0),
			UDataFuncLib::AwGetBoolField(JsonObj, "Infinity", false)
		);
	}
};