#include "Trading.h"
#include "Dom/JsonObject.h"

FTrading FTrading::FromJson(TSharedPtr<FJsonObject> JsonObj)
{
	FTrading Res = FTrading();
	Res.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id");
	Res.FailSFX = UDataFuncLib::AwGetStringField(JsonObj, "FailSFX");
	if (JsonObj->Has<PERSON>ield("Currency"))
	{
		for (TSharedPtr<FJsonValue, ESPMode::ThreadSafe> CInfo : JsonObj->GetArrayField("Currency"))
		{
			Res.CurrencyIconPath.Add(
				CInfo->AsObject()->GetStringField("Id"),
				CInfo->AsObject()->GetStringField("Icon")
			);
		} 
	}

	if (JsonObj->HasField("OnDeal"))
	{
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> OnDealFunc : JsonObj->GetArrayField("OnDeal"))
		{
			Res.OnDeal.Add(UCallFuncLib::StringToJsonFuncData(OnDealFunc->AsString()));
		} 
	}

	if (JsonObj->HasField("Deals"))
	{
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> DealInfo : JsonObj->GetArrayField("Deals"))
		{
			Res.Deals.Add(FDeal::FromJson(DealInfo->AsObject()));
		} 
	}

	return Res;
}

FTrading FTrading::DoOnDeal(FJsonFuncData JsonFuncData, FTrading Trading, FDeal Deal)
{
	FTrading ResTrading;
	UFunction* Func = UCallFuncLib::JsonFuncToUFunc(JsonFuncData);
	if (IsValid(Func) == false)
		return ResTrading;
				
	struct {
		FTrading Trading;
		FDeal Deal;

		FTrading Result;
	} FuncParam;
				
	FuncParam.Trading = Trading;
	FuncParam.Deal = Deal;
				
	UObject* TempObject = NewObject<UObject>();
	if (TempObject)
	{
		TempObject->ProcessEvent(Func, &FuncParam);
	}
				
	return FuncParam.Result;
}

FTrading FTrading::DoOnTimeElapsed(FJsonFuncData JsonFuncData, FTrading Trading)
{
	FTrading ResTrading;
	UFunction* Func = UCallFuncLib::JsonFuncToUFunc(JsonFuncData);
	if (IsValid(Func) == false)
		return ResTrading;
				
	struct {
		FTrading Trading;
			
		FTrading Result;
	} FuncParam;
		
	FuncParam.Trading = Trading;
				
	UObject* TempObject = NewObject<UObject>();
	if (TempObject)
	{
		TempObject->ProcessEvent(Func, &FuncParam);
	}
		
	return FuncParam.Result;
}

void FTrading::ModifyAllPrice(float Percentage)
{
	for (int i = 0; i < Deals.Num(); i++)
	{
		for (int j = 0; j < Deals[i].Price.Num(); j++)
		{
			Deals[i].Price[j].Count = FMath::RoundToInt(Percentage * Deals[i].Price[j].Count);
		}
	} 
}