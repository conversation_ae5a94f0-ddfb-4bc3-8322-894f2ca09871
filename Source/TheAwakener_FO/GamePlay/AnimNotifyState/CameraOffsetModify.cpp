// Fill out your copyright notice in the Description page of Project Settings.


#include "CameraOffsetModify.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/FunctionLibrary/DateTimeFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UCameraOffsetModify::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyBegin(MeshComp, Animation, TotalDuration, EventReference);


	
	const AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me || !UGameplayFuncLib::GetAwGameState() || !UGameplayFuncLib::GetAwGameState()->GetMyCharacter() ||
		Me != UGameplayFuncLib::GetAwGameState()->GetMyCharacter())
			return;

	const FAwCameraPosInfo CameraPosInfo = FAwCameraPosInfo(
		Modifer.ChangeSpeed,
		Modifer.ResetSpeed,
		Modifer.ArmLengthModify,
		Modifer.SocketCameraOffsetModify);

	UniqueId = UDateTimeFuncLib::CreateUniqueId("CamOffsetNotify");
	auto pc = UGameplayFuncLib::GetPlayerControllerByComp(MeshComp);
	if (pc)
		pc->CurCharacter->GetAwCameraComponent()->ArmModifers.Add(UniqueId, CameraPosInfo);
}

void UCameraOffsetModify::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyEnd(MeshComp, Animation, EventReference);
	
	const AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me || !UGameplayFuncLib::GetAwGameState() || !UGameplayFuncLib::GetAwGameState()->GetMyCharacter() ||
		Me != UGameplayFuncLib::GetAwGameState()->GetMyCharacter())
			return;
	auto pc = UGameplayFuncLib::GetPlayerControllerByComp(MeshComp);
	if (pc)
		pc->CurCharacter->GetAwCameraComponent()->RemoveArmModiferById(UniqueId);
}
