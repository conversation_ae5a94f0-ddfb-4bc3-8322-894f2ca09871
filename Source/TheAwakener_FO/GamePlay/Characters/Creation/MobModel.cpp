// Fill out your copyright notice in the Description page of Project Settings.


#include "MobModel.h"

#include "Engine/World.h"
#include "Kismet/KismetSystemLibrary.h"


FMobModel FMobModel::FromJson(TSharedPtr<FJsonObject> JsonObj)
	{
		FMobModel Model = FMobModel();

		Model.Id = UDataFuncLib::AwGetStringField(JsonObj, "Id");
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> TagStr : UDataFuncLib::AwGetArrayField(JsonObj, "Tag"))
			Model.Tag.Add(TagStr->AsString());

		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> AIStr : UDataFuncLib::AwGetArrayField(JsonObj, "AI"))
			Model.AIScript.Add(AIStr->AsString());

		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> AIOrderModel : UDataFuncLib::AwGetArrayField(JsonObj, "AIOrder"))
			Model.AIOrder.Add(FAIOrderModel::FromJson(AIOrderModel->AsObject()));

		Model.StartFightingWillLevel = UDataFuncLib::AwGetNumberField(JsonObj, "StartFightingWillLevel", 1);

		Model.MaxFightingWillLevel = UDataFuncLib::AwGetNumberField(JsonObj, "MaxFightingWillLevel", 2);

		if (JsonObj->HasField("MoveProp"))
			Model.MoveProp = FMobMoveProp::FromJson(JsonObj->GetObjectField("MoveProp"));

		if (JsonObj->HasField("PerceptionProp"))
			Model.PerceptionProp = FMobPerceptionProp::FromJson(JsonObj->GetObjectField("PerceptionProp"));

		Model.LootPackageId = UDataFuncLib::AwGetStringField(JsonObj, "LootPackageId");
		
		Model.BaseProp = FChaProp::PotentialFromJson(JsonObj->GetObjectField("Potential"));
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> BuffInfo : UDataFuncLib::AwGetArrayField(JsonObj, "Buff"))
			Model.InitBuff.Add(FTableAddBuffInfo::FromJson(BuffInfo->AsObject()));
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> PartInfo : UDataFuncLib::AwGetArrayField(JsonObj, "Part"))
			Model.Part.Add(FChaPart::FromJson(PartInfo->AsObject()));

		if (JsonObj->HasField("StateActions"))
		{
			const TSharedPtr<FJsonObject> PreorderKeys = JsonObj->GetObjectField("StateActions");
			for (TTuple<FString, TSharedPtr<FJsonValue, ESPMode::ThreadSafe>> KeyKV : PreorderKeys->Values)
			{
				TMap<EArmState, FString> UseActId;
				UseActId.Add(EArmState::Armed, UDataFuncLib::AwGetStringField(KeyKV.Value->AsObject(), "Armed", ""));
				UseActId.Add(EArmState::Unarmed, UDataFuncLib::AwGetStringField(KeyKV.Value->AsObject(), "Unarmed", ""));
				// if (UseActId[EArmState::Armed].IsEmpty() || UseActId[EArmState::Unarmed].IsEmpty())
				// 	UKismetSystemLibrary::PrintString(GWorld, FString("[MobModel]StateActionError:").Append(Model.Id),
				// 		true, true, FLinearColor::Red, 10);
				Model.StateActions.Add(KeyKV.Key, UseActId);
			}
		}
		
		if (JsonObj->HasField("PreorderActionKeys"))
		{
			const TSharedPtr<FJsonObject> PreorderKeys = JsonObj->GetObjectField("PreorderActionKeys");
			for (TTuple<FString, TSharedPtr<FJsonValue, ESPMode::ThreadSafe>> KeyKV : PreorderKeys->Values)
			{
				TMap<EArmState, FString> UseActId;
				UseActId.Add(EArmState::Armed, UDataFuncLib::AwGetStringField(KeyKV.Value->AsObject(), "Armed", ""));
				UseActId.Add(EArmState::Unarmed, UDataFuncLib::AwGetStringField(KeyKV.Value->AsObject(), "Unarmed", ""));
				if (UseActId[EArmState::Armed].IsEmpty() || UseActId[EArmState::Unarmed].IsEmpty())
					UKismetSystemLibrary::PrintString(GWorld, FString("[MobModel]StateActionError:").Append(Model.Id),
						true, true, FLinearColor::Red, 10);
				Model.PreorderActionKeys.Add(KeyKV.Key, UseActId);
			}
		}

		Model.ControlState = FControlState();
		if (JsonObj->HasField("ControlState"))
		{
			const TSharedPtr<FJsonObject> CsJsonObj = JsonObj->GetObjectField("ControlState");
			Model.ControlState.CanMove = UDataFuncLib::AwGetEnumField<EControlStateType>(
				CsJsonObj, "CanMove", EControlStateType::Normal);
			Model.ControlState.CanRotate = UDataFuncLib::AwGetEnumField<EControlStateType>(
				CsJsonObj, "CanRotate", EControlStateType::Normal);
			Model.ControlState.CanJump = UDataFuncLib::AwGetEnumField<EControlStateType>(
				CsJsonObj, "CanJump", EControlStateType::Normal);
			Model.ControlState.CanChangeAction = UDataFuncLib::AwGetEnumField<EControlStateType>(
				CsJsonObj, "CanChangeAction", EControlStateType::Normal);
		}

		Model.BaseActionType = UDataFuncLib::AwGetStringField(JsonObj, "BaseActionType");

		Model.MountsTypeRotate = UDataFuncLib::AwGetBoolField(JsonObj, "MountsTypeRotate");
		
		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> ActionInfo : UDataFuncLib::AwGetArrayField(JsonObj, "Actions"))
		{
			FActionInfo ThisAction = FActionInfo::FromJson(ActionInfo->AsObject());
			if (ThisAction.Id.IsEmpty() == false)
				Model.Actions.Add(ThisAction);
		}

		for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> AiActionDetailInfo : UDataFuncLib::AwGetArrayField(JsonObj, "AiActionDetails"))
		{
			FAiActionDetail ThisAiActionDetail = FAiActionDetail::FromJson(AiActionDetailInfo->AsObject());
			if (ThisAiActionDetail.AiActionId.IsEmpty() == false)
				Model.AiActionDetails.Add(ThisAiActionDetail);
		}


		if (JsonObj->HasField("MoveAnims"))
		{
			const TSharedPtr<FJsonObject> MaJsonObj = JsonObj->GetObjectField("MoveAnims");
			Model.MoveAnims.Add("MoveStart", UDataFuncLib::AwGetStringField(MaJsonObj, "MoveStart"));
			Model.MoveAnims.Add("MoveLoop", UDataFuncLib::AwGetStringField(MaJsonObj, "MoveLoop"));
			Model.MoveAnims.Add("MoveEnd", UDataFuncLib::AwGetStringField(MaJsonObj, "MoveEnd"));
		}

		Model.ChaName = UDataFuncLib::AwGetStringField(JsonObj, "Name");
		Model.PortraitPath = UDataFuncLib::AwGetStringField(JsonObj, "Portrait");
		
		Model.BpPath = UDataFuncLib::AwGetStringField(JsonObj, "BpPath");

		Model.Flyable = UDataFuncLib::AwGetBoolField(JsonObj, "Flyable");

		if (JsonObj->HasField("Equipments"))
		{
			for (TSharedPtr<FJsonValue, ESPMode::ThreadSafe> EquipInfo : UDataFuncLib::AwGetArrayField(JsonObj, "Equipments"))
			{
				FString EquipmentId = UDataFuncLib::AwGetStringField(EquipInfo->AsObject(), "Id");
				const float EquipmentRate = UDataFuncLib::AwGetNumberField(EquipInfo->AsObject(), "Rate", 0.0f);
				if (EquipmentId.IsEmpty() == false && EquipmentRate > 0)
				{
					Model.InitEquipments.Add(FMobEquipmentInfo(EquipmentId, EquipmentRate));
				}
			} 
		}

		if (JsonObj->HasField("HideSightParts"))
		{
			for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> HideSight : UDataFuncLib::AwGetArrayField(JsonObj, "HideSightParts"))
			{
				Model.HideSightPartsOnCreate.Add(HideSight->AsString());
			} 
		}

		if (JsonObj->HasField("NotInitChaParts"))
		{
			for (const TSharedPtr<FJsonValue, ESPMode::ThreadSafe> HideChaPart : JsonObj->GetArrayField("NotInitChaParts"))
			{
				Model.ChaPartsNotDefaultActive.Add(HideChaPart->AsString());
			} 
		}

		if (JsonObj->HasField("DungeonCampImpact"))
		{
			for (TSharedPtr<FJsonValue, ESPMode::ThreadSafe> DImpact : JsonObj->GetArrayField("DungeonCampImpact"))
			{
				FMobDungeonCampImpact Imp = FMobDungeonCampImpact(
					UDataFuncLib::AwGetStringField(DImpact->AsObject(), "DungeonId"),
					UDataFuncLib::AwGetStringField(DImpact->AsObject(), "CampId"),
					UDataFuncLib::AwGetNumberField(DImpact->AsObject(), "ModValue", 0)
				);
				if (Imp.DungeonId.IsEmpty() == false && Imp.CampId.IsEmpty() == false && Imp.Value != 0)
				{
					Model.DungeonCampImpacts.Add(Imp);
				}
			} 
		}

		if (JsonObj->HasField("OnBeKilled"))
		{
			for (TSharedPtr<FJsonValue, ESPMode::ThreadSafe> KilledRecall : JsonObj->GetArrayField("OnBeKilled"))
			{
				Model.OnBeKilled.Add(KilledRecall->AsString());
			} 
		}

		Model.SoundBaseDictionary = UDataFuncLib::AwGetStringField(JsonObj, "SoundBase");
		Model.SoundSocket = UDataFuncLib::AwGetStringField(JsonObj, "SoundSocket");

		Model.ExpGiven = UDataFuncLib::AwGetNumberField(JsonObj, "ExpGiven", 1);

		Model.MobRank = UDataFuncLib::AwGetEnumField<EMobRank>(JsonObj, "MobRank", EMobRank::Normal);

		return Model;
	}
