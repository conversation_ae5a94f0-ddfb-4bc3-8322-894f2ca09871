// Fill out your copyright notice in the Description page of Project Settings.


#include "SetToArms.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void USetToArms::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);

	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me || Me->Dead(true)) return;

	Me->SetToArms(this->ToArms);
	Me->RefreshEquipmentAppearance();
}
