// Fill out your copyright notice in the Description page of Project Settings.


#include "AnimationFX.h"

#include "Components/SkeletalMeshComponent.h"
#include "Engine/Classes/Sound/SoundBase.h"
#include "Kismet/GameplayStatics.h"
#include "Particles/ParticleSystem.h"
#include "TheAwakener_FO/FunctionLibrary/ResourceFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UAnimationFX::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);

	if (VFXKey.IsEmpty() && SFXKey.IsEmpty()) return;

	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;

	const FString GenderKey = CheckGender && Me->CharacterObj.Gender != ECharacterGender::None ? (Me->CharacterObj.Gender == ECharacterGender::Female ? "Female" : "Male"): FString();
	const FString EquipmentKey = Me->EquipmentFXKey(CheckEquipmentAtPart, EquipmentFXIndex);
	const FString TerrainKey = FString();	//TODO : 地面关键字未获得

	if (VFXKey.IsEmpty() == false)
	{
		FString VFXPath = VFXKey;
		if (EquipmentKey.IsEmpty() == false) VFXPath.Append("_" + EquipmentKey);
		if (TerrainKey.IsEmpty() == false) VFXPath.Append("_" + TerrainKey);
		if (TailNumber >= 0) VFXPath.Append("_" + FString::FromInt(TailNumber));
		UParticleSystem* PS = LoadObject<UParticleSystem>(nullptr,*UResourceFuncLib::GetAssetPath(VFXPath));
		if (PS) UGameplayStatics::SpawnEmitterAttached(PS, MeshComp, SocketName);
	}
	if (SFXKey.IsEmpty() == false)
	{
		FString SFXPath = SFXKey;
		if (GenderKey.IsEmpty() == false) SFXPath.Append("_" + GenderKey);
		if (EquipmentKey.IsEmpty() == false) SFXPath.Append("_" + EquipmentKey);
		if (TerrainKey.IsEmpty() == false) SFXPath.Append("_" + TerrainKey);
		if (TailNumber >= 0) SFXPath.Append("_" + FString::FromInt(TailNumber));
		USoundBase* Sound = LoadObject<USoundBase>(nullptr,*UResourceFuncLib::GetAssetPath(SFXPath));
		if (Sound) UGameplayStatics::SpawnSoundAttached(Sound, MeshComp, SocketName);
	}
}
