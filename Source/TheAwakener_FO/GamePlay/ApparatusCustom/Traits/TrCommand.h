#pragma once

// #include "CoreMinimal.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"
#include "TrCommand.generated.h"
USTRUCT()
struct THEAWAKENER_FO_API FHitLogPlayer
{
	GENERATED_BODY();
public:
	AAwCharacter* Player = nullptr;
	bool bIsHit = false;
	bool bIsBullet = false;
	FActionChangeInfo ActionChangeInfo;
	UPROPERTY()
	FAwForceFeedBackInfo ForceFeedBackInfo;
	void Reset()
	{
		bIsHit = false;
		bIsBullet = true;
	}
	void SetInfo(AAwCharacter* Character, FActionChangeInfo InActionChangeInfo,
	FAwForceFeedBackInfo FeedBackInfo,bool InIsBullet)
	{
		bIsHit = true;
		Player = Character;
		ActionChangeInfo = InActionChangeInfo;
		ForceFeedBackInfo = FeedBackInfo;
		bIsBullet = bIsBullet && InIsBullet;
	}
};
USTRUCT()
struct THEAWAKENER_FO_API FTr_HitLogItem
{
	GENERATED_BODY();
public:
	bool Enabled = false;
	int8 CanHitTimes = 0;
	double Duration = 0;
};
USTRUCT()
struct THEAWAKENER_FO_API FTr_HitLog
{
	GENERATED_BODY();
public:
	TMap<EFlagmarkBit, FTr_HitLogItem> Pool;
	FTr_HitLog()
	{
		if (Keys.IsEmpty())
		{
			// 要和 UFlagmarkBitPool 统一
			Keys = {
				EFlagmarkBit::H, EFlagmarkBit::I, EFlagmarkBit::J, EFlagmarkBit::K, EFlagmarkBit::L,
				EFlagmarkBit::M, EFlagmarkBit::N, EFlagmarkBit::O, EFlagmarkBit::P, EFlagmarkBit::Q
			};
		}

		for (EFlagmarkBit Key : Keys)
		{
			Pool.Add(Key, FTr_HitLogItem());
		}
	}
	static inline TArray<EFlagmarkBit> Keys;
	void CleanHitLogItem(EFlagmarkBit Flag);
};

inline void FTr_HitLog::CleanHitLogItem(EFlagmarkBit Flag)
{
	Pool[Flag].Enabled = false;
	Pool[Flag].CanHitTimes = 0;
	Pool[Flag].Duration = 0;
}

USTRUCT(BlueprintType,Category="Animation Command")
struct THEAWAKENER_FO_API FAC_Attack
{
	GENERATED_BODY();
};
USTRUCT(BlueprintType,Category="Animation Command")
struct THEAWAKENER_FO_API FAC_Hit
{
	GENERATED_BODY();
};
USTRUCT(BlueprintType,Category="Animation Command")
struct THEAWAKENER_FO_API FAC_FaceTo
{
	GENERATED_BODY();
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	FVector Position;
};
USTRUCT(BlueprintType,Category="Animation Command")
struct THEAWAKENER_FO_API FAC_HitUp
{
	GENERATED_BODY();
};
USTRUCT(BlueprintType,Category="Animation Command")
struct THEAWAKENER_FO_API FAC_Landed
{
	GENERATED_BODY();
};
USTRUCT(BlueprintType,Category="Animation Command")
//攻击冻结帧，避免移动
struct THEAWAKENER_FO_API FAC_AirLag
{
	GENERATED_BODY();
	float Duration = 0.3f; 	
};
USTRUCT(BlueprintType,Category="Animation Command")
struct THEAWAKENER_FO_API FAC_Run
{
	GENERATED_BODY();
};
USTRUCT(BlueprintType,Category="Animation Command")
struct THEAWAKENER_FO_API FAC_Idle
{
	GENERATED_BODY();
};

USTRUCT(BlueprintType,Category="Animation Command")
struct THEAWAKENER_FO_API FAC_Approaching
{
	GENERATED_BODY()
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	float Speed = 0.0f;
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	FVector TargetLocation;
	UPROPERTY(BlueprintReadWrite,EditAnywhere)
	float TargetRadius = 0.0f;
};