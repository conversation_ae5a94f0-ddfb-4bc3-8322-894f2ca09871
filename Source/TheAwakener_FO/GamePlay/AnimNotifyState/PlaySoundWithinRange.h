// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Animation/AnimNotifies/AnimNotifyState.h"
#include "Components/AudioComponent.h"
#include "Sound/SoundCue.h"
#include "PlaySoundWithinRange.generated.h"

/**
 * 
 */
UCLASS()
class THEAWAKENER_FO_API UPlaySoundWithinRange : public UAnimNotifyState
{
	GENERATED_BODY()

public:

	//角色蓝图里的音频组件，没有创建一个
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	FString AudioComponentName;

	UPROPERTY()
	UAudioComponent* AudioComponent;

	//开始时的音频
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	USoundCue* BeginSound;

	//循环的音频
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	USoundCue* LoopSound;

	//结束时的音频
	UPROPERTY(EditAnywhere,BlueprintReadWrite)
	USoundCue* EndSound;

	
	virtual void NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration, const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyTick(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float FrameDeltaTime, const FAnimNotifyEventReference& EventReference) override;
	virtual void NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, const FAnimNotifyEventReference& EventReference) override;
};
