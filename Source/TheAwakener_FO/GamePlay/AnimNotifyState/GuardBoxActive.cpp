// Fill out your copyright notice in the Description page of Project Settings.


#include "GuardBoxActive.h"

#include "Components/SkeletalMeshComponent.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UGuardBoxActive::NotifyBegin(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation, float TotalDuration,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyBegin(MeshComp, Animation, TotalDuration, EventReference);

	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	for (const FString BoxName : this->GuardBoxNames)
	{
		auto Box = Me->GetCharacterHitBoxByName(BoxName);
		if (Box.Get<1>() && Box.Get<1>()->Type == ECharacterHitBoxType::Guard)
		{
			Box.Get<1>()->Active = true;
			Box.Get<0>()->SetActive(Box.Get<1>()->Active);
		}
	}
}
 
void UGuardBoxActive::NotifyEnd(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::NotifyEnd(MeshComp, Animation, EventReference);

	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	for (const FString BoxName : this->GuardBoxNames)
	{
		auto Box = Me->GetCharacterHitBoxByName(BoxName);
		if (Box.Get<1>() && Box.Get<1>()->Type == ECharacterHitBoxType::Guard)
		{
			Box.Get<1>()->Active = Box.Get<1>()->DefaultActive;
			Box.Get<0>()->SetActive(Box.Get<1>()->Active);
		}
	}
}