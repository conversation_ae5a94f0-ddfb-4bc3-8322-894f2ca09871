// Fill out your copyright notice in the Description page of Project Settings.


#include "TriggerAIScript.h"
#include "Components/SkeletalMeshComponent.h"

#include "TheAwakener_FO/FunctionLibrary/CallFuncLib.h"
#include "TheAwakener_FO/GamePlay/Characters/AwCharacter.h"

void UTriggerAIScript::Notify(USkeletalMeshComponent* MeshComp, UAnimSequenceBase* Animation,
	const FAnimNotifyEventReference& EventReference)
{
	Super::Notify(MeshComp, Animation, EventReference);

	AAwCharacter* Me = Cast<AAwCharacter>(MeshComp->GetOwner());
	if (!Me) return;
	UAwAIComponent* AIComponent = Me->GetAIComponent();
	if (!AIComponent) return;

	//这段后面要弃用
	for (FString Scr : this->AIScriptFunctions)
	{
		const FJsonFuncData JsonFuncData = UDataFuncLib::SplitFuncNameAndParams(Scr);

		UFunction* ActionFunc = UCallFuncLib::GetUFunction(JsonFuncData.ClassPath, JsonFuncData.FunctionName);
		if (ActionFunc)
		{
			struct
			{
				AAwCharacter* Character;
				UAwAIComponent* AIComponent;
				TArray<FString> Params;
				FAICommand Result;
			}ActionFuncParam;
			ActionFuncParam.Character = Me;
			ActionFuncParam.AIComponent = AIComponent;
			ActionFuncParam.Params =JsonFuncData.Params;
			Me->ProcessEvent(ActionFunc, &ActionFuncParam);
			
		}
	}
	//以后只用这段
	for (FJsonFuncData ScriptFunction : AIScriptFunctionList)
	{
		UFunction* ActionFunc = UCallFuncLib::GetUFunction(ScriptFunction.ClassPath, ScriptFunction.FunctionName);
		if (ActionFunc)
		{
			struct
			{
				AAwCharacter* Character;
				UAwAIComponent* AIComponent;
				TArray<FString> Params;
				FAICommand Result;
			}ActionFuncParam;
			ActionFuncParam.Character = Me;
			ActionFuncParam.AIComponent = AIComponent;
			ActionFuncParam.Params = ScriptFunction.Params;
			Me->ProcessEvent(ActionFunc, &ActionFuncParam);
			
		}
	}
}
