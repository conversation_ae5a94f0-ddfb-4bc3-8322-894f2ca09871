// Copyright 2024 JBernardic. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Animation/DebugSkelMeshComponent.h"
#include "Modules/ModuleManager.h"

class FToolBarBuilder;
class FMenuBuilder;

constexpr const char* VATK_PluginName = "VertexAnimationToolkit";

class VERTEXANIMATIONTOOLKIT_API FVertexAnimationToolkitModule : public IModuleInterface
{
public:

	/** IModuleInterface implementation */
	virtual void StartupModule() override;
	virtual void ShutdownModule() override;
	
	/** This function will be bound to Command. */
	void PluginButtonClicked();
	
private:

	struct FAnimationSection
	{
		int32 StartFrame;
		int32 EndFrame;
		FString Name;
		FAnimationSection(int32 StartFrame = 0, int32 EndFrame = 0, FString Name = "") : StartFrame(StartFrame), EndFrame(EndFrame), Name(Name)
		{
		}
	};

	struct FConfiguration {
		bool bBoneAnimations;
	};

	void RegisterMenus();
	UObject* GetCurrentAsset();
	UDebugSkelMeshComponent* GetDebugSkelMeshComponent(UAnimationAsset* AnimationAsset);
	bool ExtractVATData(UDebugSkelMeshComponent* PreviewMesh, UAnimationAsset* AnimationAsset, TArray<FVector>& OutVertPos, TArray<FVector>& OutVertNormal, int32& OutVertSize, TArray<FAnimationSection>& OutSections, float& OutFrameRate);
	bool ExportPositionTexture(const TArray<FVector>& Verts, int32 Width, int32 MaxWidth, int32 MaxHeight, UTexture2D*& OutTexture);
	bool ExportNormalTexture(const TArray<FVector>& Verts, int32 Width, int32 MaxWitdth, int32 MaxHeight, UTexture2D*& OutTexture);
	bool MergeTextures(const FString& PackagePath, const FString& Name, TArray<UTexture2D*> Textures);
	bool ExportStaticMesh(const int AdditionalTextureIndexOffset, FString PackagePath, FString Name, UDebugSkelMeshComponent* SkelMesh, UStaticMesh*& OutStaticMesh);
	void SetForcedLOD(UDebugSkelMeshComponent* PreviewMesh, int32 lod);
	void SetRefPose(UDebugSkelMeshComponent* PreviewMesh);
	bool ExportPlayHelper(FString PackagePath, FString Name, TArray<FAnimationSection>& Sections, float FrameRate);
	
	TOptional<FConfiguration> ShowConfigurationModal();
	UObject* LoadPluginContent(const FString& Path);

	TSharedPtr<class FUICommandList> PluginCommands;
	FDelegateHandle ModuleLoadedDelegateHandle;
};
