// Fill out your copyright notice in the Description page of Project Settings.


#include "ArmorSlot.h"

#include "Engine/Texture2D.h"
#include "TheAwakener_FO/FunctionLibrary/GameplayFuncLib.h"
#include "TheAwakener_FO/FunctionLibrary/ResourceFuncLib.h"


FReply UArmorSlot::NativeOnFocusReceived(const FGeometry& InGeometry, const FFocusEvent& InFocusEvent)
{
	FReply Res = Super::NativeOnFocusReceived(InGeometry, InFocusEvent);

	UKismetSystemLibrary::PrintString(this, FString("On Me"));

	return Res;
}

void UArmorSlot::NativeOnFocusLost(const FFocusEvent& InFocusEvent)
{
	Super::NativeOnFocusLost(InFocusEvent);

	UKismetSystemLibrary::PrintString(this, FString("Leave Me Alone"));
}

void UArmorSlot::NativeOnInitialized()
{
	Super::NativeOnInitialized();
	ArmorIconImage = Cast<UImage>(GetWidgetFromName(TEXT("ArmorIcon")));
	SelectedMaskImage = Cast<UImage>(GetWidgetFromName(TEXT("SelectedMask")));
	ThisButton = Cast<UButton>(GetWidgetFromName(TEXT("BackButton")));
	if (SelectedMaskImage) SelectedMaskImage->SetVisibility(ESlateVisibility::Hidden);
}

void UArmorSlot::SetAsUsingEquipment(FEquipment ThisEquipment)
{
	this->IndexInRole = -1;
	this->Equipment = ThisEquipment;
	ArmorUIInfo = FThingUIInfo::AsEquipment(-1, ThisEquipment.Id);
	Draw();
}

void UArmorSlot::SetAsNothing()
{
	this->IndexInRole = -1;
	this->Equipment = FEquipment();
	ArmorUIInfo = FThingUIInfo();
	Draw();
}

void UArmorSlot::SetAsEquipmentInBackpack(int Index)
{
	if (Index < 0) Index = 0;
	if (Index > UAwGameInstance::Instance->RoleInfo.EquipmentObjs.Num()) Index = UAwGameInstance::Instance->RoleInfo.EquipmentObjs.Num() - 1;
	this->IndexInRole = Index;
	if (Index < 0)
	{
		Equipment = FEquipment();
		ArmorUIInfo = FThingUIInfo();
	}else
	{
		Equipment = UAwGameInstance::Instance->RoleInfo.EquipmentObjs[Index];
		ArmorUIInfo = FThingUIInfo::AsEquipment(IndexInRole);
	}
	if (this->ThisButton)
	{
		this->ThisButton->OnClicked.AddDynamic(this, &UArmorSlot::TryWearThisEquipment);
	}
	Draw();
}

void UArmorSlot::Draw()
{
	if (ArmorIconImage && this->ArmorUIInfo.Icon.IsEmpty() == false)
	{
		UTexture2D* IconTexture = LoadObject<UTexture2D>(nullptr,*UResourceFuncLib::GetAssetPath( this->ArmorUIInfo.Icon));
		if (IconTexture)
		{
			ArmorIconImage->SetBrushFromTexture(IconTexture);
			ArmorIconImage->SetVisibility(ESlateVisibility::HitTestInvisible);
		}else
		{
			ArmorIconImage->SetVisibility(ESlateVisibility::Hidden);
		}
	}
}

void UArmorSlot::NativeOnListItemObjectSet(UObject* ListItemObject)
{
	// IUserObjectListEntry::NativeOnListItemObjectSet(ListItemObject);

	const UArmorSlot* WSlot = Cast<UArmorSlot>(ListItemObject);
	this->Equipment = WSlot->Equipment;
	this->ArmorUIInfo = WSlot->ArmorUIInfo;
	this->IndexInRole = WSlot->IndexInRole;
	this->ThisButton->OnClicked = WSlot->ThisButton->OnClicked;
	
	Draw();
}

void UArmorSlot::NativeOnItemSelectionChanged(bool bIsSelected)
{
	SelectedMaskImage->SetVisibility(
		bIsSelected ? ESlateVisibility::HitTestInvisible : ESlateVisibility::Hidden
	);
	if (bIsSelected)
		UKismetSystemLibrary::PrintString(this, "[" + this->ArmorUIInfo.Name +"] Has Been Selected");
}

void UArmorSlot::TryWearThisEquipment()
{
	AAwCharacter* Me = UGameplayFuncLib::GetAwGameState()->GetMyCharacter();
	if (Me && this->IndexInRole >= 0)
	{
		Me->WearEquipment(UAwGameInstance::Instance->RoleInfo.EquipmentObjs[IndexInRole]);
	}
}