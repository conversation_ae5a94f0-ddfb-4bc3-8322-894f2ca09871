// Fill out your copyright notice in the Description page of Project Settings.

#pragma once
#include "GameFramework/Actor.h"
#include "CoreMinimal.h"
#include "AwMapScenePosition.generated.h"

/**
 * 策划在场景上拉的点
 */
UCLASS(BlueprintType,Blueprintable,hideCategories=(Rendering, Physics, LOD, Activation, Input, Actor, Cooking, Collision))
class THEAWAKENER_FO_API AAwMapScenePosition : public AActor
{
	GENERATED_BODY()
protected:
	virtual void BeginPlay() override;
private:
	void SetToGameData();
public:	
	AAwMapScenePosition();

	UPROPERTY(VisibleAnywhere, BlueprintReadWrite)
	USceneComponent* SceneComponent;

	//这个路径点的Id，用于寻路的数据中
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	FString NodeId;

	virtual void Tick(float DeltaTime) override;
};

/**
 * 地图上的点信息，这是AMapScenePosition产生的数据
 */
USTRUCT(BlueprintType)
struct FAwMapPathNode
{
	GENERATED_BODY()
public:
	//点的Id，会被其他对象引用到
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FString NodeId;

	//点的世界坐标
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	FVector Position = FVector::ZeroVector;

	FAwMapPathNode(){}
	FAwMapPathNode(FString Id, FVector Pos): NodeId(Id), Position(Pos){}

	static FAwMapPathNode FromScenePosition(const AAwMapScenePosition* ScenePos);
};
