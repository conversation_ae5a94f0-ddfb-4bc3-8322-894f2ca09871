#pragma once

#include "CoreMinimal.h" // Add this line to include FName definition
#include "Editor.h"
#include "Engine/World.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "BlueprintBatchProcessor.generated.h"

UCLASS()
class UBlueprintBatchProcessor: public UBlueprintFunctionLibrary
{
    GENERATED_BODY()

public:
    UFUNCTION(BlueprintCallable)
    static void ProcessAllBlueprints(TArray<FName> PathFilters);
    UFUNCTION(BlueprintCallable)
    static void ProcessCurrentLevelBlueprints();

private:
    static void ProcessAll(FTopLevelAssetPath ClassPath,TArray<FName> PathFilters);
    static UWorld* GetEditorWorld()
    {
        if (GEditor)
        {
            return GEditor->GetEditorWorldContext().World();
        }
        return nullptr;
    }
    static ULevelScriptBlueprint* GetCurrentLevelScriptBlueprint()
    {
        UWorld* EditorWorld = GetEditorWorld();
        if (EditorWorld && EditorWorld->PersistentLevel)
        {
            return EditorWorld->PersistentLevel->GetLevelScriptBlueprint();
        }
        return nullptr;
    }
};


