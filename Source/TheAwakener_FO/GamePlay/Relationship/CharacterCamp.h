// Fill out your copyright notice in the Description page of Project Settings.

#pragma once
#include "Dom/JsonObject.h"

#include "CoreMinimal.h"
#include "CharacterCamp.generated.h"

/**
 * 战场上角色（AAwCharacter）之间的阵营关系
 */
USTRUCT(BlueprintType)
struct FCharacterCamp
{
	GENERATED_BODY()
public:
	//阵营id
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	uint8 CampId = 0;
	
	//包含的Side
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<uint8> Sides;

	//本阵营可以攻击的阵营，这里特指的攻击盒碰到的时候，是否视为敌对（IsEnemy()==true)
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<uint8> CanAttackCampId;

	/**
	 * 本阵营可以交互的SideId（注意这个是Sides，而非Camp）
	 * 只要对方的InBattle不是true，就可以在交互范围内交互
	 */
	UPROPERTY(BlueprintReadWrite, EditAnywhere)
	TArray<uint8> CanInteractSideId;

	FCharacterCamp(){};

	static FCharacterCamp FromJson(TSharedPtr<FJsonObject> JsonObj);

};
