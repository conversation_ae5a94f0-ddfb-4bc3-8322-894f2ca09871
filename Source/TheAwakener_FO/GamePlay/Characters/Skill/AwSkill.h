// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "Dom/JsonObject.h"
#include "CoreMinimal.h"
#include "TheAwakener_FO/FunctionLibrary/DataFuncLib.h"
#include "AwSkill.generated.h"
/**
 * 
 */

UENUM(BlueprintType)
enum class EAwActionSkillType :uint8
{
	//通常
	Normal,
	//觉醒
	Awake
};

USTRUCT(BlueprintType)
struct FAwActionSkillInfo
{
	GENERATED_BODY()
	//按现有架构 实际的使用和行为都是跟着动作走的 
	UPROPERTY(EditAnywhere,BlueprintReadWrite,Category="ActionSkill")
		FString Id = "";
	UPROPERTY(EditAnywhere,BlueprintReadWrite,Category="ActionSkill")
		FString Desc = "";
	UPROPERTY(EditAnywhere,BlueprintReadWrite,Category="ActionSkill")
		EAwActionSkillType SkillType = EAwActionSkillType::Normal;
	//技能Tag用于复杂分类判断
	UPROPERTY(EditAnywhere,BlueprintReadWrite,Category="ActionSkill")
		TArray<FString> Tags;
	//最小消耗 避免秒男 不确定是否真的需要
	UPROPERTY(EditAnywhere,BlueprintReadWrite,Category="ActionSkill")
		int MinEnergyCost = 0;
	//持续消耗  持续为0就是非持续
	UPROPERTY(EditAnywhere,BlueprintReadWrite,Category="ActionSkill")
		int EnergyCostPerSecond = 0;
	//实际动作的ID
	UPROPERTY(EditAnywhere,BlueprintReadWrite,Category="ActionSkill")
		FString SkillActionId = "";
	//关闭持续性技能动作的ID
	UPROPERTY(EditAnywhere,BlueprintReadWrite,Category="ActionSkill")
		FString SkillCloseActionId = "";
	//开关型技能
	UPROPERTY(EditAnywhere,BlueprintReadWrite,Category="ActionSkill")
		bool bUsing = false;
	//UI使用的Icon
	UPROPERTY(EditAnywhere,BlueprintReadWrite,Category="ActionSkill")
		FString IconPath = "";
	//解锁使用的资源Id
	UPROPERTY(BlueprintReadOnly,EditAnywhere,Category="RogueTalent")
		FString CostSourceId = "";
	//解锁使用的资源数量
	UPROPERTY(BlueprintReadOnly,EditAnywhere,Category="RogueTalent")
		int UnlockCostNum = 0;
public:
	static FAwActionSkillInfo FromJson(TSharedPtr<FJsonObject> JsonObj);

	void ReSet();
};



